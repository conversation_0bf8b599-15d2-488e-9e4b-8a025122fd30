<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supervisor Dashboard</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/supervisor/dashboard" class="active">Dashboard</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Supervisor Dashboard</h1>

        <div class="stats-grid responsive-stats">
            <div class="stat-card">
                <h3>Pending Reports</h3>
                <p><%= stats.pendingReports %></p>
            </div>
            <div class="stat-card">
                <h3>Approved Reports</h3>
                <p><%= stats.approvedReports %></p>
            </div>
            <div class="stat-card">
                <h3>Rejected Reports</h3>
                <p><%= stats.rejectedReports %></p>
            </div>
            <div class="stat-card">
                <h3>Total Reports</h3>
                <p><%= stats.totalReports %></p>
            </div>
        </div>

        <!-- Pending Reports Section -->
        <h2>Reports Pending Review</h2>
        <div class="reports-table responsive-table supervisor-dashboard">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Intern</th>
                            <th>Unit</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Issue Details</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (pendingReports.length === 0) { %>
                            <tr>
                                <td colspan="7" class="no-reports">No pending reports to review</td>
                            </tr>
                        <% } else { %>
                            <% pendingReports.forEach(report => { %>
                                <tr data-report-id="<%= report._id %>">
                                    <td data-label="Date"><%= new Date(report.timestamp).toLocaleDateString() %></td>
                                    <td data-label="Intern"><%= report.username %></td>
                                    <td data-label="Unit"><%= report.unit %></td>
                                    <td data-label="Title"><%= report.title %></td>
                                    <td data-label="Category"><%= report.category %></td>
                                    <td data-label="Issue Details">
                                        <div class="truncated-text">
                                            <%= report.issueDetails.length > 50 ? report.issueDetails.substring(0, 50) + '...' : report.issueDetails %>
                                        </div>
                                        <button class="btn-view" onclick="viewIssueDetails('<%= report._id %>')">View Full Details</button>
                                    </td>
                                    <td data-label="Actions">
                                        <button class="btn-approve" onclick="showReviewModal('<%= report._id %>', 'approve')">Approve</button>
                                        <button class="btn-reject" onclick="showReviewModal('<%= report._id %>', 'reject')">Reject</button>
                                    </td>
                                </tr>
                            <% }) %>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Recently Reviewed Reports Section -->
        <h2>Recently Reviewed Reports</h2>
        <div class="reports-table responsive-table supervisor-dashboard">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Reviewed</th>
                            <th>Intern</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (reviewedReports.length === 0) { %>
                            <tr>
                                <td colspan="7" class="no-reports">No reviewed reports yet</td>
                            </tr>
                        <% } else { %>
                            <% reviewedReports.forEach(report => { %>
                                <tr data-report-id="<%= report._id %>" class="<%= report.supervisorStatus === 'approved' ? 'approved-row' : 'rejected-row' %>">
                                    <td data-label="Date"><%= new Date(report.timestamp).toLocaleDateString() %></td>
                                    <td data-label="Reviewed"><%= new Date(report.reviewedAt).toLocaleDateString() %></td>
                                    <td data-label="Intern"><%= report.username %></td>
                                    <td data-label="Title"><%= report.title %></td>
                                    <td data-label="Category"><%= report.category %></td>
                                    <td data-label="Status" class="<%= report.supervisorStatus === 'approved' ? 'status-approved' : 'status-rejected' %>">
                                        <%= report.supervisorStatus.charAt(0).toUpperCase() + report.supervisorStatus.slice(1) %>
                                    </td>
                                    <td data-label="Comments">
                                        <div class="truncated-text">
                                            <%= report.supervisorComments.length > 30 ? report.supervisorComments.substring(0, 30) + '...' : (report.supervisorComments || 'No comments') %>
                                        </div>
                                        <% if (report.supervisorComments && report.supervisorComments.length > 30) { %>
                                            <button class="btn-view" onclick="viewComments('<%= report._id %>', '<%= report.supervisorComments %>')">View Full Comments</button>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }) %>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Review Modal -->
    <div id="reviewModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="reviewModalTitle">Review Report</h2>
            <div class="modal-body">
                <div id="reportDetails"></div>
                <div class="form-group">
                    <label for="reviewComments">Comments:</label>
                    <textarea id="reviewComments" rows="4" placeholder="Add your comments here..."></textarea>
                </div>
                <div class="modal-actions">
                    <button id="confirmReviewBtn" class="btn">Confirm</button>
                    <button class="btn btn-cancel close-modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Issue Details Modal -->
    <div id="issueDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Issue Details</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Comments Modal -->
    <div id="commentsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Supervisor Comments</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <script src="/js/main.js"></script>
    <script>
        // Variables to store current report and action
        let currentReportId = null;
        let currentAction = null;

        // Show review modal
        function showReviewModal(reportId, action) {
            currentReportId = reportId;
            currentAction = action;
            
            // Update modal title based on action
            document.getElementById('reviewModalTitle').textContent = 
                action === 'approve' ? 'Approve Report' : 'Reject Report';
            
            // Style the confirm button based on action
            const confirmBtn = document.getElementById('confirmReviewBtn');
            confirmBtn.className = action === 'approve' ? 'btn btn-approve' : 'btn btn-reject';
            confirmBtn.textContent = action === 'approve' ? 'Approve' : 'Reject';
            
            // Clear previous comments
            document.getElementById('reviewComments').value = '';
            
            // Fetch and display report details
            fetchReportDetails(reportId);
            
            // Show the modal
            document.getElementById('reviewModal').style.display = 'block';
        }

        // Fetch report details
        async function fetchReportDetails(reportId) {
            try {
                const response = await fetch(`/supervisor/report-details/${reportId}`);
                const data = await response.json();
                
                if (data.success) {
                    const report = data.report;
                    const detailsHtml = `
                        <div class="report-detail-item">
                            <strong>Title:</strong> ${report.title}
                        </div>
                        <div class="report-detail-item">
                            <strong>Submitted By:</strong> ${report.username}
                        </div>
                        <div class="report-detail-item">
                            <strong>Date:</strong> ${new Date(report.createdAt).toLocaleString()}
                        </div>
                        <div class="report-detail-item">
                            <strong>Category:</strong> ${report.category}
                        </div>
                        <div class="report-detail-item">
                            <strong>Unit:</strong> ${report.unit}
                        </div>
                        <div class="report-detail-item">
                            <strong>Issue Details:</strong>
                            <p>${report.issueDetails}</p>
                        </div>
                        ${report.images && report.images.length > 0 ? `
                            <div class="report-detail-item">
                                <strong>Attachments:</strong>
                                <div class="attachments-preview">
                                    ${report.images.map(img => `
                                        <a href="${img.path}" target="_blank" class="attachment-item">
                                            <img src="${img.path}" alt="Attachment">
                                        </a>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    `;
                    
                    document.getElementById('reportDetails').innerHTML = detailsHtml;
                }
            } catch (error) {
                console.error('Error fetching report details:', error);
                document.getElementById('reportDetails').innerHTML = '<p>Error loading report details</p>';
            }
        }

        // Submit review
        document.getElementById('confirmReviewBtn').addEventListener('click', async function() {
            if (!currentReportId || !currentAction) return;
            
            const comments = document.getElementById('reviewComments').value;
            
            try {
                const response = await fetch(`/supervisor/review-report/${currentReportId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: currentAction,
                        comments: comments
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Remove the report from pending list
                    const reportRow = document.querySelector(`tr[data-report-id="${currentReportId}"]`);
                    if (reportRow) {
                        reportRow.remove();
                    }
                    
                    // Close modal
                    document.getElementById('reviewModal').style.display = 'none';
                    
                    // Show success notification
                    showNotification(`Report ${currentAction}d successfully`, 'success');
                    
                    // Reload page after a short delay to update stats and lists
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('Error submitting review:', error);
                showNotification(error.message || 'Error submitting review', 'error');
            }
        });

        // View issue details
        function viewIssueDetails(reportId) {
            fetch(`/supervisor/report-details/${reportId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const report = data.report;
                        const modalBody = document.querySelector('#issueDetailsModal .modal-body');
                        
                        modalBody.innerHTML = `
                            <h3>${report.title}</h3>
                            <p><strong>Submitted by:</strong> ${report.username}</p>
                            <p><strong>Date:</strong> ${new Date(report.createdAt).toLocaleString()}</p>
                            <p><strong>Category:</strong> ${report.category}</p>
                            <p><strong>Unit:</strong> ${report.unit}</p>
                            <div class="issue-details-content">
                                <p>${report.issueDetails}</p>
                            </div>
                            ${report.images && report.images.length > 0 ? `
                                <div class="attachments-section">
                                    <h4>Attachments</h4>
                                    <div class="attachments-grid">
                                        ${report.images.map(img => `
                                            <a href="${img.path}" target="_blank" class="attachment-item">
                                                <img src="${img.path}" alt="Attachment">
                                                <span>${img.originalName || 'Attachment'}</span>
                                            </a>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        `;
                        
                        document.getElementById('issueDetailsModal').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error fetching issue details:', error);
                });
        }

        // View comments
        function viewComments(reportId, comments) {
            const modalBody = document.querySelector('#commentsModal .modal-body');
            modalBody.innerHTML = `<p>${comments}</p>`;
            document.getElementById('commentsModal').style.display = 'block';
        }

        // Close modals when clicking on X or outside
        document.querySelectorAll('.close, .close-modal').forEach(element => {
            element.addEventListener('click', function() {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        window.addEventListener('click', function(event) {
            document.querySelectorAll('.modal').forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Show notification function
        function showNotification(message, type = 'info') {
            // Check if notification container exists, if not create it
            let notificationContainer = document.querySelector('.notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.className = 'notification-container';
                document.body.appendChild(notificationContainer);
            }
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            `;
            
            // Add to container
            notificationContainer.appendChild(notification);
            
            // Add close button functionality
            notification.querySelector('.notification-close').addEventListener('click', function() {
                notification.remove();
            });
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
