const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Report = require('../models/Report');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Unit = require('../models/Unit');

// Intern authentication middleware
const checkInternAuth = async (req, res, next) => {
    try {
        if (!req.session.user || req.session.user.role !== 'intern') {
            return res.redirect('/login');
        }
        if (!req.session.user.isActive) {
            req.session.destroy();
            return res.redirect('/login?error=Account is disabled');
        }
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.redirect('/login');
    }
};

// Configure multer for file upload
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = '../client/public/uploads/reports';
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, `${uniqueSuffix}${path.extname(file.originalname)}`);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
        if (!allowedTypes.includes(file.mimetype)) {
            cb(new Error('Invalid file type'), false);
            return;
        }
        cb(null, true);
    }
});

// Apply middleware to all intern routes
router.use(checkInternAuth);

// Intern dashboard route
router.get('/dashboard', async (req, res) => {
    try {
        const userId = req.session.user.id;

        // Get user-specific statistics
        const [totalReports, solvedReports, pendingReports, recentReports] = await Promise.all([
            Report.countDocuments({ user: userId }),
            Report.countDocuments({ user: userId, status: 'Solved' }),
            Report.countDocuments({
                user: userId,
                status: { $in: ['Unsolved', 'In Progress'] }
            }),
            Report.find({ user: userId })
                .populate({
                    path: 'supervisorId',
                    select: 'username'
                })
                .sort({ createdAt: -1 })
                .lean()
        ]);

        // Format the reports data
        const formattedReports = recentReports.map(report => ({
            _id: report._id,
            timestamp: report.createdAt,
            title: report.title,
            category: report.category,
            unit: report.unit,
            status: report.status,
            issueDetails: report.issueDetails,
            images: report.images || [],
            supervisorStatus: report.supervisorStatus || 'pending',
            supervisorName: report.supervisorId ? report.supervisorId.username : 'Not Assigned',
            supervisorComments: report.supervisorComments || ''
        }));

        res.render('intern/dashboard', {
            user: req.session.user,
            stats: {
                totalReports,
                solvedReports,
                pendingReports
            },
            reports: formattedReports
        });

    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', {
            message: 'Error loading dashboard'
        });
    }
});

// Submit report route
router.post('/submit-report', upload.array('images', 3), async (req, res) => {
    try {
        const { title, category, unit, issueDetails } = req.body;

        // Process uploaded files
        const images = req.files ? req.files.map(file => ({
            filename: file.filename,
            path: `/uploads/reports/${file.filename}`,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size
        })) : [];

        // Create new report
        const report = new Report({
            title,
            category,
            unit,
            issueDetails,
            images,
            user: req.session.user.id,
            status: 'Pending Supervisor Review'
        });

        await report.save();

        res.status(201).json({
            success: true,
            message: 'Report submitted successfully',
            reportId: report._id
        });

    } catch (error) {
        console.error('Submit report error:', error);

        // Clean up any uploaded files if there's an error
        if (req.files) {
            req.files.forEach(file => {
                fs.unlink(file.path, err => {
                    if (err) console.error('Error deleting file:', err);
                });
            });
        }

        res.status(500).json({
            success: false,
            message: 'Error submitting report'
        });
    }
});

// Update report status route
router.patch('/update-report-status/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const userId = req.session.user.id;

        const report = await Report.findOneAndUpdate(
            { _id: id, user: userId },
            { status },
            { new: true }
        );

        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        res.json({
            success: true,
            message: 'Status updated successfully'
        });

    } catch (error) {
        console.error('Update status error:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating status'
        });
    }
});

// Add this new route for the submit report page
router.get('/submit-report', async (req, res) => {
    try {
        res.render('intern/submit-report', {
            user: req.session.user
        });
    } catch (error) {
        console.error('Submit report page error:', error);
        res.status(500).render('error', {
            message: 'Error loading submit report page'
        });
    }
});

// Add this route to get updated dashboard stats
router.get('/dashboard-stats', async (req, res) => {
    try {
        const userId = req.session.user.id;

        const [totalReports, solvedReports, pendingReports] = await Promise.all([
            Report.countDocuments({ user: userId }),
            Report.countDocuments({ user: userId, status: 'Solved' }),
            Report.countDocuments({
                user: userId,
                status: { $in: ['Unsolved', 'In Progress'] }
            })
        ]);

        res.json({
            totalReports,
            solvedReports,
            pendingReports
        });

    } catch (error) {
        console.error('Stats error:', error);
        res.status(500).json({ error: 'Error fetching stats' });
    }
});


// Add this route to get all units
router.get('/units', async (req, res) => {
    try {
        const units = await Unit.find({ active: true })
            .sort({ name: 1 })
            .select('name');

        res.json({
            success: true,
            units
        });
    } catch (error) {
        console.error('Get units error:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching units'
        });
    }
});

module.exports = router;
