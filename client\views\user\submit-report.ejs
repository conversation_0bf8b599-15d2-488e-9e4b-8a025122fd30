<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Report</title>
    <link rel="stylesheet" href="/css/style.css">
    <!-- Add TinyMCE for rich text editing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.3/tinymce.min.js"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXCH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/user/dashboard">Dashboard</a>
            <a href="/user/submit-report" class="active">Submit Report</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Submit New Report</h1>

        <div class="report-form">
            <form id="reportForm" action="/user/submit-report" method="POST">
                <div class="form-row">
                    <div class="form-group short-select">
                        <label for="category">Category</label>
                        <select id="category" name="category" required>
                            <option value="">Select</option>
                            <option value="Hardware">Hardware</option>
                            <option value="Network">Network</option>
                            <option value="Software">Software</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="form-group short-select">
                        <label for="unit">Unit</label>
                        <select id="unit" name="unit" required>
                            <option value="">Select Unit</option>
                            <option value="Emergency">Emergency</option>
                            <option value="OPD">OPD</option>
                            <option value="IPD">IPD</option>
                            <option value="Laboratory">Laboratory</option>
                            <option value="Pharmacy">Pharmacy</option>
                            <option value="Radiology">Radiology</option>
                            <option value="Administration">Administration</option>
                        </select>
                    </div>

                    <div class="form-group short-select">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="Unsolved">Unsolved</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Solved">Solved</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="title">Title</label>
                    <input type="text" id="title" name="title" required 
                           placeholder="Brief description of the issue"
                           maxlength="100">
                </div>

                <div class="form-group">
                    <label for="issueDetails">Issue Details</label>
                    <div class="editor-container">
                        <textarea id="issueDetails" 
                                  name="issueDetails" 
                                  class="hidden-textarea"
                                  required></textarea>
                        <div class="resize-handle"></div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary" id="submitBtn">Submit Report</button>
            </form>
        </div>

        <div class="my-reports">
            <h2>My Recent Reports</h2>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% reports.forEach(report => { %>
                        <tr data-report-id="<%= report._id %>">
                            <td><%= new Date(report.timestamp).toLocaleDateString() %></td>
                            <td><%= report.title %></td>
                            <td><%= report.category %></td>
                            <td class="report-status"><%= report.status %></td>
                            <td>
                                <select class="status-select" 
                                        onchange="updateReportStatus('<%= report._id %>', this.value)"
                                        <%= report.status === 'Solved' ? 'disabled' : '' %>>
                                    <option value="Unsolved" <%= report.status === 'Unsolved' ? 'selected' : '' %>>Unsolved</option>
                                    <option value="In Progress" <%= report.status === 'In Progress' ? 'selected' : '' %>>In Progress</option>
                                    <option value="Solved" <%= report.status === 'Solved' ? 'selected' : '' %>>Solved</option>
                                </select>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Initialize TinyMCE with modern configuration
        tinymce.init({
            selector: '#issueDetails',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
                'searchreplace', 'code', 'fullscreen', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
            setup: function(editor) {
                editor.on('init', function() {
                    editor.setContent(''); // Remove initial paragraph tag
                });
                // Handle form validation
                editor.on('change', function() {
                    const content = editor.getContent();
                    const textarea = document.getElementById('issueDetails');
                    textarea.value = content;
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                });
            },
            forced_root_block: false, // Prevent automatic paragraph tags
            valid_elements: 'strong,em,span[style],a[href],ul,ol,li,br,p,h1,h2,h3',
            valid_styles: {
                '*': 'font-size,font-family,color,text-decoration,text-align'
            }
        });

        // Form validation and submission
        document.getElementById('reportForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get TinyMCE content
            const content = tinymce.get('issueDetails').getContent();
            if (!content || content === '<p></p>' || content === '') {
                alert('Please enter issue details');
                return;
            }

            if (isSubmitting) return;
            isSubmitting = true;

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';

            try {
                const formData = {
                    title: document.getElementById('title').value,
                    category: document.getElementById('category').value,
                    unit: document.getElementById('unit').value,
                    status: document.getElementById('status').value,
                    issueDetails: content
                };

                const response = await fetch('/user/submit-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (response.ok) {
                    window.location.href = data.redirect;
                } else {
                    alert(data.message || 'Error submitting report');
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Submit Report';
                    isSubmitting = false;
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to submit report');
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Report';
                isSubmitting = false;
            }
        });

        // Function to update report status
        async function updateReportStatus(reportId, newStatus) {
            try {
                const response = await fetch(`/user/report/${reportId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                if (response.ok) {
                    const row = document.querySelector(`tr[data-report-id="${reportId}"]`);
                    const statusCell = row.querySelector('.report-status');
                    statusCell.textContent = newStatus;
                    
                    // Disable select if status is Solved
                    if (newStatus === 'Solved') {
                        row.querySelector('.status-select').disabled = true;
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to update report status');
            }
        }

        // Editor resize functionality
        const editorContainer = document.querySelector('.editor-container');
        const resizeHandle = document.querySelector('.resize-handle');
        let startY, startHeight;

        resizeHandle.addEventListener('mousedown', initResize);

        function initResize(e) {
            startY = e.clientY;
            startHeight = parseInt(document.defaultView.getComputedStyle(tinymce.get('issueDetails').getContainer()).height, 10);
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopResize);
            e.preventDefault();
        }

        function resize(e) {
            const newHeight = startHeight + (e.clientY - startY);
            if (newHeight > 200) { // Minimum height
                tinymce.get('issueDetails').getContainer().style.height = newHeight + 'px';
            }
        }

        function stopResize() {
            document.removeEventListener('mousemove', resize);
            document.removeEventListener('mouseup', stopResize);
        }
    </script>
    <script src="/js/main.js"></script>
</body>
</html> 