<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/responsive.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/admin/dashboard">Dashboard</a>
            <a href="/admin/user-management" class="active">User Management</a>
            <a href="/admin/report-management">Report Management</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>User Management</h1>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="showAddUnitModal()">Add New Unit</button>
        </div>

        <div class="add-user-form responsive-form">
            <h2>Add New User</h2>
            <form action="/admin/add-user" method="POST">
                <div class="form-row">
                    <div class="form-group short-input">
                        <label for="username">Username (3-15 chars)</label>
                        <input type="text"
                               id="username"
                               name="username"
                               minlength="3"
                               maxlength="15"
                               pattern="[a-zA-Z0-9]+"
                               title="Username must be between 3-15 characters and contain only letters and numbers"
                               required>
                    </div>

                    <div class="form-group short-input">
                        <label for="password">Password (6-12 chars)</label>
                        <input type="password"
                               id="password"
                               name="password"
                               minlength="6"
                               maxlength="12"
                               required>
                    </div>

                    <div class="form-group short-select">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="intern">Intern</option>
                            <option value="supervisor">Supervisor</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Add User</button>
            </form>
        </div>

        <div class="users-table responsive-table user-management">
            <h2>Existing Users</h2>
            <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Role</th>
                        <th>Password</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% users.forEach(user => { %>
                        <tr data-user-id="<%= user._id %>">
                            <td data-label="Username"><%= user.username %></td>
                            <td data-label="Role"><%= user.role %></td>
                            <td data-label="Password" class="password-cell">
                                <% if (user.role === 'intern' || user.role === 'supervisor') { %>
                                    <span class="password-hidden">••••••</span>
                                    <span class="password-visible" style="display: none;"><%= user.plainPassword %></span>
                                    <button class="btn btn-small btn-toggle-password" onclick="togglePassword(this)">
                                        Show
                                    </button>
                                <% } else { %>
                                    N/A
                                <% } %>
                            </td>
                            <td data-label="Status" class="user-status"><%= user.isActive ? 'Active' : 'Disabled' %></td>
                            <td data-label="Created At"><%= new Date(user.createdAt).toLocaleDateString() %></td>
                            <td data-label="Actions">
                                <button
                                    class="btn <%= user.isActive ? 'btn-disable' : 'btn-enable' %>"
                                    onclick="toggleUserStatus('<%- user._id %>', <%- user.isActive %>)">
                                    <%= user.isActive ? 'Disable' : 'Enable' %>
                                </button>
                                <button class="btn btn-small btn-update-password"
                                        onclick="showPasswordModal('<%= user._id %>', '<%= user.username %>')">
                                    Update Password
                                </button>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
            </div><!-- end table-container -->
        </div>
    </div>

    <!-- Add Unit Modal -->
    <div id="addUnitModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Add New Unit</h2>
            <form id="addUnitForm">
                <div class="form-group">
                    <label for="unitName">Unit Name</label>
                    <input type="text"
                           id="unitName"
                           name="unitName"
                           required
                           pattern="[A-Za-z0-9\s]+"
                           title="Only letters, numbers and spaces allowed"
                           maxlength="50">
                    <small class="form-text">Unit name must be unique and contain only letters, numbers, and spaces</small>
                </div>
                <button type="submit" class="btn btn-primary" id="addUnitBtn">
                    <span class="btn-text">Add Unit</span>
                    <span class="btn-loader" style="display: none;">
                        <div class="spinner"></div>
                    </span>
                </button>
            </form>
        </div>
    </div>

    <!-- Password Update Modal -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Update Password</h2>
            <form id="updatePasswordForm" onsubmit="handlePasswordUpdate(event)">
                <input type="hidden" id="userId" name="userId">
                <div class="form-group">
                    <label for="newPassword">New Password (6-12 characters)</label>
                    <input type="password"
                           id="newPassword"
                           name="newPassword"
                           required
                           minlength="6"
                           maxlength="12"
                           pattern="^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,12}$"
                           title="Password must be 6-12 characters long and include at least one letter and one number">
                    <div class="password-strength-meter"></div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password"
                           id="confirmPassword"
                           name="confirmPassword"
                           required>
                </div>
                <button type="submit" class="btn btn-primary">Update Password</button>
            </form>
        </div>
    </div>

    <script>
        async function toggleUserStatus(userId, currentStatus) {
            try {
                const button = document.querySelector(`tr[data-user-id="${userId}"] button:not(.btn-toggle-password)`);
                const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                const username = row.querySelector('td:first-child').textContent;

                // Confirm action
                const action = currentStatus ? 'disable' : 'enable';
                const confirmMessage = `Are you sure you want to ${action} ${username}'s account?`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                // Show loading state
                button.textContent = 'Processing...';
                button.disabled = true;

                const response = await fetch(`/admin/users/${userId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Failed to update user status');
                }

                // Update UI
                    const statusCell = row.querySelector('.user-status');

                // Update status text and classes
                    statusCell.textContent = data.isActive ? 'Active' : 'Disabled';
                statusCell.className = 'user-status ' + (data.isActive ? 'status-active' : 'status-disabled');

                // Update button
                    button.textContent = data.isActive ? 'Disable' : 'Enable';
                button.className = 'btn ' + (data.isActive ? 'btn-disable' : 'btn-enable');

                // Update onclick handler
                    button.setAttribute('onclick', `toggleUserStatus('${userId}', ${data.isActive})`);
                button.disabled = false;

                // Show notification
                showNotification(
                    `${username}'s account has been ${data.isActive ? 'enabled' : 'disabled'} successfully`,
                    'success'
                );

            } catch (error) {
                console.error('Error:', error);
                showNotification('Failed to update user status', 'error');

                // Reset button state
                const button = document.querySelector(`tr[data-user-id="${userId}"] button:not(.btn-toggle-password)`);
                if (button) {
                    button.textContent = currentStatus ? 'Disable' : 'Enable';
                    button.disabled = false;
                }
            }
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function togglePassword(button) {
            const cell = button.parentElement;
            const hiddenSpan = cell.querySelector('.password-hidden');
            const visibleSpan = cell.querySelector('.password-visible');

            if (hiddenSpan.style.display !== 'none') {
                hiddenSpan.style.display = 'none';
                visibleSpan.style.display = 'inline';
                button.textContent = 'Hide';
                } else {
                hiddenSpan.style.display = 'inline';
                visibleSpan.style.display = 'none';
                button.textContent = 'Show';
            }
        }

        // Add form submit handler
        document.querySelector('.add-user-form form').addEventListener('submit', async function(e) {
            e.preventDefault();

            try {
                const formData = new FormData(this);

                const response = await fetch('/admin/add-user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: formData.get('username'),
                        password: formData.get('password'),
                        role: formData.get('role')
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Error creating user');
                }

                // Show success message
                showNotification('User created successfully', 'success');

                // Add new user to table
                const userTable = document.querySelector('.users-table tbody');
                const newRow = document.createElement('tr');
                newRow.dataset.userId = data.user.id;
                newRow.innerHTML = `
                    <td data-label="Username">${data.user.username}</td>
                    <td data-label="Role">${data.user.role}</td>
                    <td data-label="Password" class="password-cell">
                        ${data.user.role === 'intern' || data.user.role === 'supervisor' ? `
                            <span class="password-hidden">••••••</span>
                            <span class="password-visible" style="display: none;">${formData.get('password')}</span>
                            <button class="btn btn-small btn-toggle-password" onclick="togglePassword(this)">
                                Show
                            </button>
                        ` : 'N/A'}
                    </td>
                    <td data-label="Status" class="user-status">Active</td>
                    <td data-label="Created At">${new Date().toLocaleDateString()}</td>
                    <td data-label="Actions">
                        <button
                            class="btn btn-disable"
                            onclick="toggleUserStatus('${data.user.id}', true)">
                            Disable
                        </button>
                        <button class="btn btn-small btn-update-password"
                                onclick="showPasswordModal('${data.user.id}', '${data.user.username}')">
                            Update Password
                        </button>
                    </td>
                `;
                userTable.insertBefore(newRow, userTable.firstChild);

                // Reset form
                this.reset();

            } catch (error) {
                console.error('Error:', error);
                showNotification(error.message || 'Failed to create user', 'error');
            }
        });

        // Show modal function
        function showAddUnitModal() {
            document.getElementById('addUnitModal').style.display = 'block';
        }

        // Form submission handler
        document.getElementById('addUnitForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoader = submitBtn.querySelector('.btn-loader');
            const unitName = document.getElementById('unitName').value.trim();

            // Clear any existing notifications
            clearNotifications();

            if (!unitName) {
                showNotification('Unit name is required', 'error');
                return;
            }

            try {
                // Show loading state
                submitBtn.disabled = true;
                btnText.style.display = 'none';
                btnLoader.style.display = 'flex';

                const response = await fetch('/admin/add-unit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ unitName })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Failed to add unit');
                }

                // Update dropdowns with new unit list
                if (data.allUnits) {
                    updateAllUnitDropdowns(data.allUnits);
                }

                // Show success message and reset form
                showNotification('Unit added successfully', 'success');
                this.reset();
                document.getElementById('addUnitModal').style.display = 'none';

            } catch (error) {
                showNotification(error.message || 'Error adding unit', 'error');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                btnText.style.display = 'block';
                btnLoader.style.display = 'none';
            }
        });

        // Function to update all unit dropdowns
        function updateAllUnitDropdowns(units) {
            const unitSelects = document.querySelectorAll('select[name="unit"]');

            unitSelects.forEach(select => {
                const currentValue = select.value;
                const firstOption = select.querySelector('option:first-child');
                select.innerHTML = '';

                // Preserve placeholder option if it exists
                if (firstOption && !firstOption.value) {
                    select.appendChild(firstOption);
                }

                // Add all units
                units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.name;
                    option.textContent = unit.name;
                    option.selected = unit.name === currentValue;
                    select.appendChild(option);
                });
            });
        }

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Clear notifications function
        function clearNotifications() {
            document.querySelectorAll('.notification').forEach(n => n.remove());
        }

        // Modal close handlers
        document.querySelector('.close').addEventListener('click', function() {
            document.getElementById('addUnitModal').style.display = 'none';
            clearNotifications();
        });

        window.addEventListener('click', function(event) {
            const modal = document.getElementById('addUnitModal');
            if (event.target === modal) {
                modal.style.display = 'none';
                clearNotifications();
            }
        });

        // Password Update Modal Functions
        function showPasswordModal(userId, username) {
            document.getElementById('userId').value = userId;
            document.getElementById('passwordModal').style.display = 'block';
        }

        async function handlePasswordUpdate(event) {
            event.preventDefault();
            const userId = document.getElementById('userId').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword !== confirmPassword) {
                showNotification('Passwords do not match', 'error');
                return;
            }

            if (!validatePassword(newPassword)) {
                showNotification('Password does not meet requirements', 'error');
                return;
            }

            if (!confirm('Are you sure you want to update this password?')) {
                return;
            }

            try {
                const response = await fetch(`/admin/users/${userId}/password`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ newPassword })
                });

                const data = await response.json();

                if (response.ok) {
                    showNotification('Password updated successfully', 'success');
                    document.getElementById('passwordModal').style.display = 'none';
                    document.getElementById('updatePasswordForm').reset();
                } else {
                    throw new Error(data.message || 'Failed to update password');
                }
            } catch (error) {
                showNotification(error.message, 'error');
            }
        }

        // Password Strength Checker
        document.getElementById('newPassword').addEventListener('input', function(e) {
            const password = e.target.value;
            const meter = document.querySelector('.password-strength-meter');

            meter.className = 'password-strength-meter';
            if (password.length >= 8 && /[A-Z]/.test(password) && /[a-z]/.test(password) && /[0-9]/.test(password)) {
                meter.classList.add('strength-strong');
            } else if (password.length >= 6 && /[A-Za-z]/.test(password) && /[0-9]/.test(password)) {
                meter.classList.add('strength-medium');
            } else if (password.length > 0) {
                meter.classList.add('strength-weak');
            }
        });

        // Utility Functions
        function validatePassword(password) {
            return /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,12}$/.test(password);
        }

        // Close modal functionality
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.onclick = function() {
                this.closest('.modal').style.display = 'none';
                clearNotifications();
            }
        });

        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                clearNotifications();
            }
        }
    </script>

    <!-- Add some styles for the password toggle -->
    <style>
        .btn-small {
            padding: 2px 8px;
            font-size: 0.8em;
            margin-left: 8px;
        }

        .password-cell {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-toggle-password {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .btn-toggle-password:hover {
            background-color: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .error-message {
            color: #dc3545;
            margin-top: 5px;
            font-size: 0.9em;
        }

        .btn-disable, .btn-enable {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-disable {
            background-color: #dc3545;
            color: white;
        }

        .btn-enable {
            background-color: #28a745;
            color: white;
        }

        .btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
        }

        .status-active {
            color: #28a745;
            font-weight: 500;
        }

        .status-disabled {
            color: #dc3545;
            font-weight: 500;
        }

        /* Add animation for status changes */
        .user-status, .btn {
            transition: all 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background-color: #198754;
        }

        .notification.error {
            background-color: #dc3545;
        }

        .notification.fade-out {
            animation: slideOut 0.3s ease forwards;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .add-user-form {
            margin-bottom: 2rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .form-group {
            flex: 1;
        }

        .form-group.short-input {
            max-width: 200px;
        }

        .form-group.short-select {
            max-width: 150px;
        }

        .action-buttons {
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            border-radius: 8px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 1.5rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .password-strength-meter {
            height: 4px;
            background-color: #e9ecef;
            margin-top: 0.5rem;
            border-radius: 2px;
        }

        .password-strength-meter::before {
            content: '';
            display: block;
            height: 100%;
            width: 0;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .strength-weak::before {
            width: 33%;
            background-color: #dc3545;
        }

        .strength-medium::before {
            width: 66%;
            background-color: #ffc107;
        }

        .strength-strong::before {
            width: 100%;
            background-color: #198754;
        }

        .btn-update-password {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 0.375rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-update-password:hover {
            background-color: #0b5ed7;
        }

        /* Add these styles */
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-loader {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
    </style>
    <script src="/js/main.js"></script>
    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc</span>
        </div>
    </footer>
</body>
</html>