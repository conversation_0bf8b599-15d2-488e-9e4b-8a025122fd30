<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intern Dashboard</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    
    <nav class="navbar">
        <div class="nav-brand">SFXCH IT Reporting System</div>
        <div class="nav-links">
            <a href="/user/dashboard" class="active">Dashboard</a>
            <a href="/user/submit-report">Submit Report</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>My Reports</h1>

        <div class="reports-table">
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Title</th>
                        <th>Unit</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% reports.forEach(report => { %>
                        <tr data-report-id="<%= report._id %>">
                            <td><%= new Date(report.timestamp).toLocaleDateString() %></td>
                            <td><%= report.title %></td>
                            <td><%= report.unit %></td>
                            <td><%= report.category %></td>
                            <td class="report-status"><%= report.status %></td>
                            <td>
                                <select class="status-select" 
                                        onchange="updateReportStatus('<%= report._id %>', this.value)"
                                        <%= report.status === 'Solved' ? 'disabled' : '' %>>
                                    <option value="Unsolved" <%= report.status === 'Unsolved' ? 'selected' : '' %>>Unsolved</option>
                                    <option value="In Progress" <%= report.status === 'In Progress' ? 'selected' : '' %>>In Progress</option>
                                    <option value="Solved" <%= report.status === 'Solved' ? 'selected' : '' %>>Solved</option>
                                </select>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
        </div>
    </div>
    <script src="/js/main.js"></script>
</body>
</html> 