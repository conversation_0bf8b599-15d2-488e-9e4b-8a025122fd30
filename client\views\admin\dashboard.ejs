<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXCH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/admin/dashboard" class="active">Dashboard</a>
            <a href="/admin/user-management">User Management</a>
            <a href="/admin/report-management">Report Management</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Admin Dashboard</h1>

        <div class="stats-grid responsive-stats">
            <div class="stat-card">
                <h3>Total Reports</h3>
                <p><%= stats.totalReports %></p>
            </div>
            <div class="stat-card">
                <h3>Total Interns</h3>
                <p><%= stats.totalInterns %></p>
            </div>
            <div class="stat-card">
                <h3>Active Interns</h3>
                <p><%= stats.activeInterns %></p>
            </div>
            <div class="stat-card">
                <h3>Total Supervisors</h3>
                <p><%= stats.totalSupervisors %></p>
            </div>
            <div class="stat-card">
                <h3>Active Supervisors</h3>
                <p><%= stats.activeSupervisors %></p>
            </div>
            <div class="stat-card">
                <h3>Approved Reports</h3>
                <p><%= stats.approvedReports %></p>
            </div>
            <div class="stat-card">
                <h3>Rejected Reports</h3>
                <p><%= stats.rejectedReports %></p>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters responsive-filters">
            <div class="filter-group">
                <label for="categoryFilter">Category</label>
                <select id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="Hardware">Hardware</option>
                    <option value="Network">Network</option>
                    <option value="Software">Software</option>
                    <option value="Other">Other</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="unitFilter">Unit</label>
                <select id="unitFilter">
                    <option value="">All Units</option>
                    <option value="Emergency">Emergency</option>
                    <option value="OPD">OPD</option>
                    <option value="IPD">IPD</option>
                    <option value="Laboratory">Laboratory</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Radiology">Radiology</option>
                    <option value="Administration">Administration</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="statusFilter">Status</label>
                <select id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="Solved">Solved</option>
                    <option value="Unsolved">Unsolved</option>
                    <option value="In Progress">In Progress</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="dateFilter">Date</label>
                <input type="date" id="dateFilter">
            </div>

            <div class="filter-group search-group">
                <label for="searchInput">Search</label>
                <input type="text" id="searchInput" placeholder="Search reports...">
        </div>
        </div>

        <!-- Reports Table -->
        <div class="reports-table responsive-table admin-dashboard">
            <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Intern</th>
                        <th>Unit</th>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Issue Details</th>
                        <th>Status</th>
                        <th>Supervisor</th>
                        <th>Supervisor Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% reports.forEach(report => { %>
                        <tr data-report-id="<%= report._id %>" data-date="<%= report.timestamp.toISOString().split('T')[0] %>">
                            <td data-label="Date" data-iso-date="<%= report.timestamp.toISOString().split('T')[0] %>"><%= new Date(report.timestamp).toLocaleDateString() %></td>
                            <td data-label="Intern"><%= report.username %></td>
                            <td data-label="Unit"><%= report.unit %></td>
                            <td data-label="Title"><%= report.title %></td>
                            <td data-label="Category"><%= report.category %></td>
                            <td data-label="Issue Details">
                                <div class="issue-details-preview">
                                    <div class="issue-text"><%= report.issueDetails.length > 100 ? report.issueDetails.substring(0, 100) + '...' : report.issueDetails %></div>
                                    <button class="btn-view-more">View More</button>
                                    <% if (report.images && report.images.length > 0) { %>
                                        <span class="attachment-indicator"
                                              data-report-id="<%= report._id %>"
                                              data-images="<%= JSON.stringify(report.images) %>">
                                            <i class="fas fa-paperclip"></i> <%= report.images.length %> attachment<%= report.images.length > 1 ? 's' : '' %>
                                        </span>
                                    <% } %>
                                </div>
                            </td>
                            <td data-label="Status" class="report-status">
                                <select class="status-select" onchange="updateReportStatus('<%= report._id %>', this.value)">
                                    <option value="Unsolved" <%= report.status === 'Unsolved' ? 'selected' : '' %>>Unsolved</option>
                                    <option value="In Progress" <%= report.status === 'In Progress' ? 'selected' : '' %>>In Progress</option>
                                    <option value="Solved" <%= report.status === 'Solved' ? 'selected' : '' %>>Solved</option>
                                    <option value="Pending Supervisor Review" <%= report.status === 'Pending Supervisor Review' ? 'selected' : '' %>>Pending Supervisor Review</option>
                                    <option value="Supervisor Approved" <%= report.status === 'Supervisor Approved' ? 'selected' : '' %>>Supervisor Approved</option>
                                    <option value="Supervisor Rejected" <%= report.status === 'Supervisor Rejected' ? 'selected' : '' %>>Supervisor Rejected</option>
                                </select>
                            </td>
                            <td data-label="Supervisor"><%= report.supervisorName %></td>
                            <td data-label="Supervisor Status" class="supervisor-status <%= report.supervisorStatus %>">
                                <span class="status-badge <%= report.supervisorStatus %>">
                                    <%= report.supervisorStatus.charAt(0).toUpperCase() + report.supervisorStatus.slice(1) %>
                                </span>
                                <% if (report.supervisorComments) { %>
                                    <button class="btn-view-comments" onclick="viewSupervisorComments('<%= report._id %>', '<%= report.supervisorComments %>')">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                <% } %>
                            </td>
                            <td data-label="Actions">
                                <button class="btn-delete" onclick="deleteReport('<%= report._id %>')">Delete</button>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
            </div><!-- end table-container -->
        </div>
    </div>

    <!-- Modal for viewing full issue details -->
    <div id="issueDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Issue Details</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Modal for viewing attachments -->
    <div id="attachmentsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Attachments</h2>
            <div class="modal-body attachments-container"></div>
        </div>
    </div>

    <!-- Modal for viewing supervisor comments -->
    <div id="supervisorCommentsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Supervisor Comments</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <script>
        // Search and Filter functionality
        function filterReports() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const category = document.getElementById('categoryFilter').value;
            const unit = document.getElementById('unitFilter').value;
            const status = document.getElementById('statusFilter').value;
            const date = document.getElementById('dateFilter').value;

            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const rowData = {
                    date: row.getAttribute('data-date'),
                    intern: row.children[1].textContent.toLowerCase(),
                    unit: row.children[2].textContent,
                    title: row.children[3].textContent.toLowerCase(),
                    category: row.children[4].textContent,
                    details: row.children[5].textContent.toLowerCase(),
                    status: row.querySelector('.status-select').value
                };

                const matchesSearch = !searchTerm ||
                    rowData.intern.includes(searchTerm) ||
                    rowData.title.includes(searchTerm) ||
                    rowData.details.includes(searchTerm);

                const matchesCategory = !category || rowData.category === category;
                const matchesUnit = !unit || rowData.unit === unit;
                const matchesStatus = !status || rowData.status === status;
                // Get the ISO date from the data attribute for reliable comparison
                const rowIsoDate = row.getAttribute('data-date');
                const filterIsoDate = date ? new Date(date).toISOString().split('T')[0] : '';
                const matchesDate = !date || rowIsoDate === filterIsoDate;

                row.style.display =
                    matchesSearch && matchesCategory && matchesUnit &&
                    matchesStatus && matchesDate ? '' : 'none';
            });
        }

        // Update report status
        async function updateReportStatus(reportId, newStatus) {
            try {
                const response = await fetch(`/admin/update-report-status/${reportId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                if (!response.ok) throw new Error('Failed to update status');

                showNotification('Status updated successfully', 'success');
            } catch (error) {
                console.error('Error:', error);
                showNotification('Failed to update status', 'error');
            }
        }

        // Delete report
        async function deleteReport(reportId) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch(`/admin/delete-report/${reportId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) throw new Error('Failed to delete report');

                document.querySelector(`tr[data-report-id="${reportId}"]`).remove();
                showNotification('Report deleted successfully', 'success');
            } catch (error) {
                console.error('Error:', error);
                showNotification('Failed to delete report', 'error');
            }
        }

        // View supervisor comments
        function viewSupervisorComments(reportId, comments) {
            const modal = document.getElementById('supervisorCommentsModal');
            const modalBody = modal.querySelector('.modal-body');

            modalBody.innerHTML = `
                <div class="supervisor-comments-content">
                    <p>${comments || 'No comments provided'}</p>
                </div>
            `;

            modal.style.display = 'block';
        }

        // Modal functionality
        const modal = document.getElementById('issueDetailsModal');
        const modalBody = modal.querySelector('.modal-body');
        const closeBtn = modal.querySelector('.close');

        document.querySelectorAll('.btn-view-more').forEach(button => {
            button.addEventListener('click', function() {
                const reportId = this.closest('tr').dataset.reportId;
                const reportRow = document.querySelector(`tr[data-report-id="${reportId}"]`);
                const details = reportRow.querySelector('.issue-text').textContent;

                // Format the text with line breaks preserved
                modalBody.innerHTML = '';
                const textElement = document.createElement('div');
                textElement.className = 'issue-details-text';

                // Replace newlines with <br> tags for proper display
                textElement.innerHTML = details.replace(/\n/g, '<br>');

                modalBody.appendChild(textElement);
                modal.style.display = 'block';
            });
        });

        closeBtn.addEventListener('click', () => modal.style.display = 'none');
        window.addEventListener('click', e => {
            if (e.target === modal) modal.style.display = 'none';
        });

        // Event listeners for filters
        document.getElementById('searchInput').addEventListener('input', filterReports);
        document.getElementById('categoryFilter').addEventListener('change', filterReports);
        document.getElementById('unitFilter').addEventListener('change', filterReports);
        document.getElementById('statusFilter').addEventListener('change', filterReports);
        document.getElementById('dateFilter').addEventListener('change', filterReports);

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

         // Add this function to fetch and populate unit dropdowns
    async function loadUnitOptions() {
        try {
            const response = await fetch('/admin/units');
            const data = await response.json();

            if (data.success) {
                const unitSelect = document.getElementById('unitFilter');
                // Keep the "All Units" option
                const allOption = unitSelect.querySelector('option[value=""]');
                unitSelect.innerHTML = '';
                unitSelect.appendChild(allOption);

                // Add units from database
                data.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.name;
                    option.textContent = unit.name;
                    unitSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading units:', error);
        }
    }

    // Call this function when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadUnitOptions();

        // Add click event listeners to attachment indicators
        document.querySelectorAll('.attachment-indicator').forEach(indicator => {
            indicator.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent triggering the view details modal

                const reportId = this.dataset.reportId;
                let images = [];

                try {
                    images = JSON.parse(this.dataset.images);
                } catch (error) {
                    console.error('Error parsing image data:', error);
                    return;
                }

                if (images.length === 0) return;

                // Populate attachments modal
                const modal = document.getElementById('attachmentsModal');
                const modalBody = modal.querySelector('.attachments-container');
                modalBody.innerHTML = '';

                images.forEach(image => {
                    const attachmentEl = document.createElement('div');
                    attachmentEl.className = 'attachment-item';

                    if (image.mimetype.startsWith('image/')) {
                        // Image attachment
                        attachmentEl.innerHTML = `
                            <div class="attachment-preview">
                                <img src="${image.path}" alt="${image.originalName}">
                            </div>
                            <div class="attachment-info">
                                <div class="attachment-name">${image.originalName}</div>
                                <div class="attachment-meta">
                                    ${formatFileSize(image.size)} • ${image.mimetype.split('/')[1].toUpperCase()}
                                </div>
                                <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                    Download
                                </a>
                            </div>
                        `;
                    } else if (image.mimetype === 'application/pdf') {
                        // PDF attachment
                        attachmentEl.innerHTML = `
                            <div class="attachment-preview pdf-preview">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="attachment-info">
                                <div class="attachment-name">${image.originalName}</div>
                                <div class="attachment-meta">
                                    ${formatFileSize(image.size)} • PDF
                                </div>
                                <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                    Download
                                </a>
                            </div>
                        `;
                    }

                    modalBody.appendChild(attachmentEl);
                });

                // Show modal
                modal.style.display = 'block';
            });
        });

        // Close attachment modal
        const attachmentsModal = document.getElementById('attachmentsModal');
        const attachmentsCloseBtn = attachmentsModal.querySelector('.close');

        attachmentsCloseBtn.addEventListener('click', () => {
            attachmentsModal.style.display = 'none';
        });

        window.addEventListener('click', e => {
            if (e.target === attachmentsModal) {
                attachmentsModal.style.display = 'none';
            }
        });
    });

    // Helper function to format file size
    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }
    </script>

    <style>
        /* Existing styles */
        .filters {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .search-group {
            flex: 2;
        }

        .status-select {
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .btn-delete {
            padding: 4px 8px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-delete:hover {
            background-color: #c82333;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .filter-group {
                min-width: 100%;
            }

            .reports-table {
                overflow-x: auto;
            }

            table {
                min-width: 800px;
            }
        }

        /* Update/add these styles */
        .reports-table {
            margin-top: 1.5rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        /* Updated table header styles */
        thead tr {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        th {
            padding: 1rem;
            text-align: left;
            color: white;
            font-weight: 600;
            font-size: 0.95rem;
            position: relative;
        }

        /* Add subtle border between headers */
        th:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 25%;
            height: 50%;
            width: 1px;
            background-color: #dee2e6;
        }

        /* Hover effect for better visibility */
        thead tr:hover {
            background-color: #f1f3f5;
        }

        /* Table body styles */
        td {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }

        tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* Status badge styles */
        .status-badge {
            display: inline-block;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
        }

        .status-solved {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-unsolved {
            background-color: #f8d7da;
            color: #842029;
        }

        .status-in-progress {
            background-color: #fff3cd;
            color: #664d03;
        }

        /* Filter section styles */
        .filters {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .filter-group label {
            color: #495057;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .filter-group select,
        .filter-group input {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 0.5rem;
            width: 100%;
        }

        /* Issue details preview styles */
        .issue-details-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            max-width: 400px;
        }

        .issue-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px;
        }

        .issue-details-text {
            white-space: pre-wrap;
            word-break: break-word;
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .btn-view-more {
            padding: 0.25rem 0.75rem;
            background-color: #a31d5b;

            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }

        .btn-view-more:hover {
            background-color:#B6DEDF;
        }

        /* Responsive styles are in responsive.css */

        /* Attachment styles are now in style.css */

        /* Attachment hover styles are now in style.css */

        /* Attachment icon styles are now in style.css */

        .attachments-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .attachment-item {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .attachment-preview {
            width: 120px;
            height: 120px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #dee2e6;
        }

        .attachment-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .pdf-preview {
            background-color: #f8f9fa;
            color: #dc3545;
            font-size: 2.5rem;
        }

        .attachment-info {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .attachment-meta {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .attachment-download {
            align-self: flex-start;
            padding: 5px 10px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .attachment-download:hover {
            background-color: #0b5ed7;
        }
    </style>

    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc</span>
        </div>
    </footer>
</body>
</html>