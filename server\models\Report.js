const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Hardware', 'Network', 'Software', 'Other']
  },
  unit: {
    type: String,
    required: true
  },
  issueDetails: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['Solved', 'Unsolved', 'In Progress', 'Pending', 'Pending Supervisor Review', 'Supervisor Approved', 'Supervisor Rejected'],
    default: 'Pending Supervisor Review'
  },
  supervisorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  supervisorStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  supervisorComments: {
    type: String
  },
  supervisorReviewedAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  images: [{
    filename: String,
    path: String,
    originalName: String,
    mimetype: String,
    size: Number,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }]
});

module.exports = mongoose.model('Report', reportSchema); 