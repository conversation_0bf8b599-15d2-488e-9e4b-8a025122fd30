const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Report = require('../models/Report');

// Supervisor authentication middleware
const checkSupervisorAuth = async (req, res, next) => {
    try {
        if (!req.session.user || req.session.user.role !== 'supervisor') {
            return res.redirect('/login');
        }
        if (!req.session.user.isActive) {
            req.session.destroy();
            return res.redirect('/login?error=Account is disabled');
        }
        next();
    } catch (error) {
        console.error('Supervisor auth middleware error:', error);
        res.redirect('/login');
    }
};

// Apply authentication middleware to all routes
router.use(checkSupervisorAuth);

// Supervisor dashboard
router.get('/dashboard', async (req, res) => {
    try {
        const supervisorId = req.session.user.id;

        // Get dashboard statistics
        const stats = await Promise.all([
            Report.countDocuments({ supervisorStatus: 'pending' }),
            Report.countDocuments({ supervisorId: supervisorId, supervisorStatus: 'approved' }),
            Report.countDocuments({ supervisorId: supervisorId, supervisorStatus: 'rejected' }),
            Report.countDocuments()
        ]);

        // Get pending reports for review
        const pendingReports = await Report.find({ 
            supervisorStatus: 'pending',
            status: 'Pending Supervisor Review'
        })
        .populate({
            path: 'user',
            select: 'username'
        })
        .sort({ createdAt: -1 })
        .lean();

        // Get reports reviewed by this supervisor
        const reviewedReports = await Report.find({ 
            supervisorId: supervisorId,
            supervisorStatus: { $in: ['approved', 'rejected'] }
        })
        .populate({
            path: 'user',
            select: 'username'
        })
        .sort({ supervisorReviewedAt: -1 })
        .limit(10)
        .lean();

        // Clean and prepare the reports data
        const cleanedPendingReports = pendingReports.map(report => ({
            _id: report._id,
            timestamp: report.createdAt,
            username: report.user ? report.user.username : 'Unknown User',
            unit: report.unit || 'N/A',
            title: report.title || 'No Title',
            category: report.category || 'Uncategorized',
            issueDetails: report.issueDetails || 'No details provided',
            status: report.status || 'Pending Supervisor Review',
            images: report.images || []
        }));

        const cleanedReviewedReports = reviewedReports.map(report => ({
            _id: report._id,
            timestamp: report.createdAt,
            reviewedAt: report.supervisorReviewedAt,
            username: report.user ? report.user.username : 'Unknown User',
            unit: report.unit || 'N/A',
            title: report.title || 'No Title',
            category: report.category || 'Uncategorized',
            issueDetails: report.issueDetails || 'No details provided',
            supervisorStatus: report.supervisorStatus,
            supervisorComments: report.supervisorComments || '',
            images: report.images || []
        }));

        // Render dashboard with data
        res.render('supervisor/dashboard', {
            user: req.session.user,
            stats: {
                pendingReports: stats[0],
                approvedReports: stats[1],
                rejectedReports: stats[2],
                totalReports: stats[3]
            },
            pendingReports: cleanedPendingReports,
            reviewedReports: cleanedReviewedReports
        });

    } catch (error) {
        console.error('Supervisor dashboard error:', error);
        res.status(500).render('error', {
            message: 'Error loading supervisor dashboard. Please try again.'
        });
    }
});

// Review report (approve/reject)
router.patch('/review-report/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { action, comments } = req.body; // action: 'approve' or 'reject'
        const supervisorId = req.session.user.id;

        if (!['approve', 'reject'].includes(action)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid action. Must be approve or reject.'
            });
        }

        const report = await Report.findById(id);
        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        // Update report with supervisor review
        report.supervisorId = supervisorId;
        report.supervisorStatus = action === 'approve' ? 'approved' : 'rejected';
        report.supervisorComments = comments || '';
        report.supervisorReviewedAt = new Date();
        
        // Update main status based on supervisor action
        if (action === 'approve') {
            report.status = 'Supervisor Approved';
        } else {
            report.status = 'Supervisor Rejected';
        }

        await report.save();

        res.json({
            success: true,
            message: `Report ${action}d successfully`,
            supervisorStatus: report.supervisorStatus,
            status: report.status
        });

    } catch (error) {
        console.error('Review report error:', error);
        res.status(500).json({
            success: false,
            message: 'Error reviewing report'
        });
    }
});

// Get report details for review modal
router.get('/report-details/:id', async (req, res) => {
    try {
        const report = await Report.findById(req.params.id)
            .populate('user', 'username')
            .lean();

        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        res.json({
            success: true,
            report: {
                _id: report._id,
                title: report.title,
                category: report.category,
                unit: report.unit,
                issueDetails: report.issueDetails,
                username: report.user ? report.user.username : 'Unknown User',
                createdAt: report.createdAt,
                images: report.images || []
            }
        });

    } catch (error) {
        console.error('Get report details error:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching report details'
        });
    }
});

module.exports = router;
