const express = require('express');
const router = express.Router();
const User = require('../models/User');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

// Rate limiting for login attempts
const rateLimit = require('express-rate-limit');
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login attempts per windowMs
    message: 'Too many login attempts, please try again after 15 minutes'
});

// Middleware to check if user is already logged in
const isNotAuthenticated = (req, res, next) => {
    if (!req.session.user) {
        return next();
    }
    res.redirect(req.session.user.role === 'admin' ? '/admin/dashboard' : '/user/dashboard');
};

// Login page
router.get('/login', isNotAuthenticated, (req, res) => {
    res.render('login', { error: req.query.error });
});

// Login process with rate limiting
router.post('/login', loginLimiter, async (req, res) => {
    try {
        const { username, password } = req.body;

        // Convert username to lowercase for case-insensitive comparison
        const lowercaseUsername = username.toLowerCase();
        
        // Find user (case-insensitive search)
        const user = await User.findOne({
            username: { $regex: new RegExp(`^${lowercaseUsername}$`, 'i') }
        });

        if (!user) {
            return res.render('login', {
                error: 'Invalid username or password',
                username // Preserve the username in the form
            });
        }

        // Check if user is disabled (for interns)
        if (user.role === 'intern' && !user.isActive) {
            return res.render('login', {
                error: 'Your account has been disabled. Please contact an administrator.',
                username
            });
        }

        // For interns and supervisors, check against plainPassword
        let isValidPassword = false;
        if (user.role === 'intern' || user.role === 'supervisor') {
            isValidPassword = user.plainPassword === password;
        } else {
            // For admins, check against hashed password
            isValidPassword = await bcrypt.compare(password, user.password);
        }

        if (!isValidPassword) {
            return res.render('login', {
                error: 'Invalid username or password',
                username
            });
        }

        // Set session
        req.session.user = {
            id: user._id,
            username: user.username,
            role: user.role,
            isActive: user.isActive
        };

        // Save session before redirect
        req.session.save((err) => {
            if (err) {
                console.error('Session save error:', err);
                return res.render('login', {
                    error: 'An error occurred during login',
                    username
                });
            }

            // Redirect based on role
            if (user.role === 'admin') {
                res.redirect('/admin/dashboard');
            } else if (user.role === 'supervisor') {
                res.redirect('/supervisor/dashboard');
            } else {
                res.redirect('/intern/dashboard');
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.render('login', {
            error: 'An error occurred during login',
            username
        });
    }
});

// Logout
router.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
        }
        res.redirect('/login');
    });
});

// Password reset request
router.post('/reset-password-request', async (req, res) => {
    try {
        const { username } = req.body;
        const user = await User.findOne({ username });

        if (!user) {
            return res.status(404).json({
                message: 'If a user exists with this username, they will receive reset instructions'
            });
        }

        // Generate reset token
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetTokenExpiry = Date.now() + 3600000; // 1 hour

        // Save reset token to user
        user.resetPasswordToken = resetToken;
        user.resetPasswordExpires = resetTokenExpiry;
        await user.save();

        // In a real application, send email with reset link
        // For now, just return success message
        res.json({
            message: 'If a user exists with this username, they will receive reset instructions'
        });
    } catch (error) {
        console.error('Password reset request error:', error);
        res.status(500).json({
            message: 'An error occurred processing your request'
        });
    }
});

// Change password (when logged in)
router.post('/change-password', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                message: 'You must be logged in to change your password'
            });
        }

        const { currentPassword, newPassword } = req.body;
        const user = await User.findById(req.session.user._id).select('+password');

        // Verify current password
        const isMatch = await user.matchPassword(currentPassword);
        if (!isMatch) {
            return res.status(401).json({
                message: 'Current password is incorrect'
            });
        }

        // Update password
        user.password = newPassword;
        await user.save();

        res.json({
            message: 'Password updated successfully'
        });
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            message: 'An error occurred while changing your password'
        });
    }
});

// Session check endpoint
router.get('/check-session', (req, res) => {
    if (req.session.user) {
        res.json({
            isAuthenticated: true,
            user: {
                username: req.session.user.username,
                role: req.session.user.role
            }
        });
    } else {
        res.json({
            isAuthenticated: false
        });
    }
});

module.exports = router;