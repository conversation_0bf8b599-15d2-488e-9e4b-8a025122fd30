const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Report = require('../models/Report');
const Unit = require('../models/Unit');
const bcrypt = require('bcryptjs');

// Middleware to check if user is admin
const isAdmin = (req, res, next) => {
    if (req.session.user && req.session.user.role === 'admin') {
        next();
    } else {
        res.status(403).json({ message: 'Access denied' });
    }
};

// Apply admin middleware to all routes
router.use(isAdmin);

// Middleware to ensure JSON responses for errors
router.use((err, req, res, next) => {
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        return res.status(400).json({
            success: false,
            message: 'Invalid JSON format'
        });
    }
    next();
});

// Admin dashboard route
router.get('/dashboard', async (req, res) => {
    try {
        // Get dashboard statistics
        const stats = await Promise.all([
            Report.countDocuments(),
            User.countDocuments({ role: 'intern' }),
            User.countDocuments({ role: 'intern', isActive: true }),
            User.countDocuments({ role: 'supervisor' }),
            User.countDocuments({ role: 'supervisor', isActive: true }),
            Report.countDocuments({ supervisorStatus: 'approved' }),
            Report.countDocuments({ supervisorStatus: 'rejected' })
        ]);

        // Get all reports with user and supervisor information
        const reports = await Report.find()
            .populate({
                path: 'user',
                select: 'username'
            })
            .populate({
                path: 'supervisorId',
                select: 'username'
            })
            .sort({ createdAt: -1 })
            .lean();

        // Clean and prepare the reports data
        const cleanedReports = reports.map(report => ({
            _id: report._id,
            timestamp: report.createdAt,
            username: report.user ? report.user.username : 'Unknown User',
            unit: report.unit || 'N/A',
            title: report.title || 'No Title',
            category: report.category || 'Uncategorized',
            issueDetails: report.issueDetails || 'No details provided',
            status: report.status || 'Unsolved',
            supervisorStatus: report.supervisorStatus || 'pending',
            supervisorName: report.supervisorId ? report.supervisorId.username : 'Not Assigned',
            supervisorComments: report.supervisorComments || '',
            images: report.images || []
        }));

        // Render dashboard with data
        res.render('admin/dashboard', {
            user: req.session.user,
            stats: {
                totalReports: stats[0],
                totalInterns: stats[1],
                activeInterns: stats[2],
                totalSupervisors: stats[3],
                activeSupervisors: stats[4],
                approvedReports: stats[5],
                rejectedReports: stats[6]
            },
            reports: cleanedReports
        });

    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', {
            message: 'Error loading dashboard. Please try again.'
        });
    }
});

// Filter reports
router.get('/filter-reports', async (req, res) => {
    try {
        const { category, status, date } = req.query;
        let query = {};

        if (category) query.category = category;
        if (status) query.status = status;
        if (date) {
            const startDate = new Date(date);
            const endDate = new Date(date);
            endDate.setDate(endDate.getDate() + 1);
            query.createdAt = { $gte: startDate, $lt: endDate };
        }

        const reports = await Report.find(query)
            .populate('user', 'username')
            .sort({ createdAt: -1 });

        res.json(reports);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Middleware to check admin authentication
const checkAdminAuth = (req, res, next) => {
    if (!req.session.user || req.session.user.role !== 'admin') {
        return res.status(401).json({
            success: false,
            message: 'Unauthorized access'
        });
    }
    next();
};

// Apply auth middleware to all admin routes
router.use(checkAdminAuth);

// GET route for user management page
router.get('/user-management', async (req, res) => {
    try {
        const users = await User.find().sort({ createdAt: -1 });
        res.render('admin/user-management', { users });
    } catch (error) {
        console.error('User management page error:', error);
        res.status(500).render('error', {
            message: 'Error loading user management page'
        });
    }
});

// Toggle user status route
router.patch('/toggle-user-status/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        const { isActive } = req.body;

        // Validate user exists
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Update user status
        user.isActive = isActive;
        await user.save();

        // Send response
        res.json({
            success: true,
            isActive: user.isActive,
            message: 'User status updated successfully'
        });

    } catch (error) {
        console.error('Error updating user status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while updating user status'
        });
    }
});

// User status toggle route
router.post('/users/:userId/toggle-status', async (req, res) => {
    try {
        const { userId } = req.params;

        // Use findOneAndUpdate to avoid validation
        const user = await User.findOneAndUpdate(
            { _id: userId },
            [{ $set: { isActive: { $not: "$isActive" } } }],
            { new: true, runValidators: false }
        );

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        return res.json({
            success: true,
            isActive: user.isActive,
            message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`
        });

    } catch (error) {
        console.error('Error toggling user status:', error);
        return res.status(500).json({
            success: false,
            message: 'Error updating user status'
        });
    }
});

// Add user route
router.post('/add-user', async (req, res) => {
    try {
        const { username, password, role } = req.body;

        // Validate username and password
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username and password are required'
            });
        }

        // Check username length
        if (username.length < 3 || username.length > 15) {
            return res.status(400).json({
                success: false,
                message: 'Username must be between 3 and 15 characters'
            });
        }

        // Check password length
        if (password.length < 6 || password.length > 12) {
            return res.status(400).json({
                success: false,
                message: 'Password must be between 6 and 12 characters'
            });
        }

        // Check if username already exists (case-insensitive)
        const existingUser = await User.findOne({
            username: { $regex: new RegExp(`^${username}$`, 'i') }
        });

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'Username already exists'
            });
        }

        // Create new user
        const newUser = new User({
            username: username.trim(), // Remove extra spaces
            password: role === 'admin' ? await bcrypt.hash(password, 10) : password,
            plainPassword: (role === 'intern' || role === 'supervisor') ? password : undefined,
            role,
            isActive: true
        });

        await newUser.save();

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: {
                id: newUser._id,
                username: newUser.username,
                role: newUser.role
            }
        });

    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating user',
            error: error.message
        });
    }
});

// Delete user
router.delete('/delete-user/:id', async (req, res) => {
    try {
        const user = await User.findById(req.params.id);

        // Prevent deleting the last admin
        if (user.role === 'admin') {
            const adminCount = await User.countDocuments({ role: 'admin' });
            if (adminCount <= 1) {
                return res.status(400).json({ message: 'Cannot delete the last admin user' });
            }
        }

        await User.findByIdAndDelete(req.params.id);
        // Also delete all reports by this user
        await Report.deleteMany({ user: req.params.id });

        res.json({ message: 'User deleted successfully' });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Update report status
router.patch('/update-report-status/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        const report = await Report.findByIdAndUpdate(
            id,
            { status },
            { new: true }
        );

        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        res.json({
            success: true,
            message: 'Status updated successfully',
            status: report.status
        });

    } catch (error) {
        console.error('Update status error:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating status'
        });
    }
});

// Report management route
router.get('/report-management', async (req, res) => {
    try {
        const reports = await Report.find()
            .populate('user', 'username')
            .sort({ createdAt: -1 })
            .lean();

        const cleanedReports = reports.map(report => ({
            ...report,
            username: report.user ? report.user.username : 'Unknown User',
            timestamp: report.createdAt,
            issueDetails: report.issueDetails || 'No details provided',
            images: report.images || []
        }));

        res.render('admin/report-management', {
            user: req.session.user,
            reports: cleanedReports
        });
    } catch (error) {
        console.error('Report management error:', error);
        res.status(500).render('error', {
            message: 'Error loading report management page'
        });
    }
});

// Delete reports route
router.delete('/delete-reports', async (req, res) => {
    try {
        const { reportIds } = req.body;

        if (!Array.isArray(reportIds) || reportIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No reports selected for deletion'
            });
        }

        await Report.deleteMany({ _id: { $in: reportIds } });

        res.json({
            success: true,
            message: 'Reports deleted successfully'
        });
    } catch (error) {
        console.error('Delete reports error:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting reports'
        });
    }
});

// Delete report route
router.delete('/delete-report/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const report = await Report.findByIdAndDelete(id);

        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        res.json({
            success: true,
            message: 'Report deleted successfully'
        });

    } catch (error) {
        console.error('Delete report error:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting report'
        });
    }
});

// Add unit route
router.post('/add-unit', async (req, res) => {
    try {
        const { unitName } = req.body;

        // Enhanced input validation
        if (!unitName || unitName.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Unit name is required'
            });
        }

        // Format unit name (capitalize first letter of each word)
        const formattedUnitName = unitName.trim()
            .toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        // Check for existing unit (case-insensitive)
        const existingUnit = await Unit.findOne({
            name: { $regex: new RegExp(`^${formattedUnitName}$`, 'i') }
        });

        if (existingUnit) {
            return res.status(409).json({  // Changed to 409 Conflict
                success: false,
                message: `Unit "${formattedUnitName}" already exists`
            });
        }

        // Create new unit
        const unit = await Unit.create({
            name: formattedUnitName
        });

        // Get updated list of all active units
        const allUnits = await Unit.find({ active: true })
            .sort({ name: 1 })
            .select('name');

        res.status(201).json({
            success: true,
            message: `Unit "${formattedUnitName}" added successfully`,
            unit,
            allUnits
        });

    } catch (error) {
        console.error('Add unit error:', error);

        // Handle mongoose duplicate key error
        if (error.code === 11000) {
            return res.status(409).json({
                success: false,
                message: 'This unit already exists'
            });
        }

        res.status(500).json({
            success: false,
            message: 'An error occurred while adding the unit'
        });
    }
});

// GET route for fetching all units
router.get('/units', async (req, res) => {
    try {
        const units = await Unit.find({ active: true })
            .sort({ name: 1 })
            .select('name');

        res.json({
            success: true,
            units
        });
    } catch (error) {
        console.error('Get units error:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching units'
        });
    }
});

// Update user password
router.patch('/users/:userId/password', async (req, res) => {
    try {
        const { userId } = req.params;
        const { newPassword } = req.body;

        // Validate password
        if (!newPassword || newPassword.length < 6 || newPassword.length > 12) {
            return res.status(400).json({ message: 'Invalid password' });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Handle different password storage based on role
        if (user.role === 'intern') {
            user.plainPassword = newPassword;
        } else {
            // For admin, hash the password
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(newPassword, salt);
        }

        await user.save();

        res.json({
            success: true,
            message: 'Password updated successfully'
        });

    } catch (error) {
        console.error('Update password error:', error);
        res.status(500).json({ message: 'Error updating password' });
    }
});

module.exports = router;