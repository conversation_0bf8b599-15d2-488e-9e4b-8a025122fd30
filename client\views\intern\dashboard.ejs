<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intern Dashboard</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/intern/dashboard" class="active">Dashboard</a>
            <a href="/intern/submit-report">Submit Report</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Welcome, <%= user.username %></h1>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Reports</h3>
                <p><%= stats.totalReports %></p>
            </div>
            <div class="stat-card">
                <h3>Solved Reports</h3>
                <p><%= stats.solvedReports %></p>
            </div>
            <div class="stat-card">
                <h3>Pending Reports</h3>
                <p><%= stats.pendingReports %></p>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters">
            <div class="filter-group">
                <label for="categoryFilter">Category</label>
                <select id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="Hardware">Hardware</option>
                    <option value="Network">Network</option>
                    <option value="Software">Software</option>
                    <option value="Other">Other</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="unitFilter">Unit</label>
                <select id="unitFilter">
                    <option value="">All Units</option>
                    <option value="Emergency">Emergency</option>
                    <option value="OPD">OPD</option>
                    <option value="IPD">IPD</option>
                    <option value="Laboratory">Laboratory</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Radiology">Radiology</option>
                    <option value="Administration">Administration</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="statusFilter">Status</label>
                <select id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="Solved">Solved</option>
                    <option value="Unsolved">Unsolved</option>
                    <option value="In Progress">In Progress</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="dateFilter">Date</label>
                <input type="date" id="dateFilter">
            </div>

            <div class="filter-group search-group">
                <label for="searchInput">Search</label>
                <input type="text" id="searchInput" placeholder="Search reports...">
            </div>
        </div>

        <!-- Reports Table -->
        <div class="reports-table intern-dashboard">
            <table id="reportsTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Unit</th>
                        <th>Issue Details</th>
                        <th>Status</th>
                        <th>Supervisor Review</th>
                    </tr>
                </thead>
                <tbody>
                    <% reports.forEach(report => { %>
                        <tr data-report-id="<%= report._id %>"
                            data-category="<%= report.category %>"
                            data-unit="<%= report.unit %>"
                            data-status="<%= report.status %>"
                            data-date="<%= new Date(report.timestamp).toISOString().split('T')[0] %>">
                            <td data-label="Date"><%= new Date(report.timestamp).toLocaleDateString() %></td>
                            <td data-label="Title"><%= report.title %></td>
                            <td data-label="Category"><%= report.category %></td>
                            <td data-label="Unit"><%= report.unit %></td>
                            <td data-label="Issue Details">
                                <div class="issue-details-preview">
                                    <span class="preview-text"><%= report.issueDetails.substring(0, 50) %>...</span>
                                    <button class="btn-view-more" onclick="viewIssueDetails(this)">View</button>
                                    <% if (report.images && report.images.length > 0) { %>
                                        <span class="attachment-indicator"
                                              data-report-id="<%= report._id %>"
                                              data-images="<%= JSON.stringify(report.images) %>">
                                            <i class="fas fa-paperclip"></i> <%= report.images.length %> attachment<%= report.images.length > 1 ? 's' : '' %>
                                        </span>
                                    <% } %>
                                </div>
                                <div class="full-details" style="display: none;">
                                    <%= report.issueDetails %>
                                </div>
                            </td>
                            <td data-label="Status">
                                <select class="status-select" onchange="updateReportStatus(this, '<%= report._id %>')"
                                        <%= report.status === 'Solved' || report.status === 'Pending Supervisor Review' ||
                                           report.status === 'Supervisor Approved' || report.status === 'Supervisor Rejected' ? 'disabled' : '' %>>
                                    <option value="Unsolved" <%= report.status === 'Unsolved' ? 'selected' : '' %>>Unsolved</option>
                                    <option value="In Progress" <%= report.status === 'In Progress' ? 'selected' : '' %>>In Progress</option>
                                    <option value="Solved" <%= report.status === 'Solved' ? 'selected' : '' %>>Solved</option>
                                    <option value="Pending Supervisor Review" <%= report.status === 'Pending Supervisor Review' ? 'selected' : '' %>>Pending Review</option>
                                    <option value="Supervisor Approved" <%= report.status === 'Supervisor Approved' ? 'selected' : '' %>>Approved</option>
                                    <option value="Supervisor Rejected" <%= report.status === 'Supervisor Rejected' ? 'selected' : '' %>>Rejected</option>
                                </select>
                            </td>
                            <td data-label="Supervisor Review">
                                <% if (report.status === 'Pending Supervisor Review') { %>
                                    <span class="supervisor-status pending">Pending Review</span>
                                <% } else if (report.status === 'Supervisor Approved') { %>
                                    <span class="supervisor-status approved">Approved</span>
                                    <% if (report.supervisorComments) { %>
                                        <button class="btn-view-comments" onclick="viewSupervisorComments('<%= report._id %>', '<%= report.supervisorComments %>')">
                                            <i class="fas fa-comment"></i>
                                        </button>
                                    <% } %>
                                <% } else if (report.status === 'Supervisor Rejected') { %>
                                    <span class="supervisor-status rejected">Rejected</span>
                                    <% if (report.supervisorComments) { %>
                                        <button class="btn-view-comments" onclick="viewSupervisorComments('<%= report._id %>', '<%= report.supervisorComments %>')">
                                            <i class="fas fa-comment"></i>
                                        </button>
                                    <% } %>
                                <% } else { %>
                                    <span class="supervisor-status na">N/A</span>
                                <% } %>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Simple Modal for Issue Details -->
    <div id="issueDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Issue Details</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Modal for viewing attachments -->
    <div id="attachmentsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Attachments</h2>
            <div class="modal-body attachments-container"></div>
        </div>
    </div>

    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin: 0 0 1rem 0;
            color: #495057;
        }

        .stat-card p {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            color:  #a31d5b;
        }

        .filters {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .search-group {
            flex: 2;
        }

        .filter-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .reports-table {
            margin-top: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .issue-details-preview {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .preview-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        .btn-view-more {
            padding: 0.25rem 0.5rem;
            background-color:  #a31d5b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .status-select {
            padding: 0.375rem 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            font-size: 0.875rem;
            transition: border-color 0.15s ease-in-out;
        }

        .status-select:disabled {
            background-color: #e9ecef;
            cursor: not-allowed;
            opacity: 0.7;
        }

        .status-select option {
            padding: 0.5rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 1.5rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .modal-body {
            margin-top: 1rem;
            max-height: 70vh;
            overflow-y: auto;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background-color: #198754;
        }

        .notification.error {
            background-color: #dc3545;
        }

        .notification.fade-out {
            animation: slideOut 0.3s ease forwards;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        /* Responsive styles are in responsive.css */

        .no-results {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-style: italic;
        }

        .report-detail {
            margin-bottom: 1rem;
        }

        .report-detail strong {
            display: inline-block;
            min-width: 100px;
            color: #495057;
        }

        .issue-content {
            margin-top: 0.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            word-break: break-word;
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-solved {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-unsolved {
            background-color: #f8d7da;
            color: #842029;
        }

        .status-in-progress {
            background-color: #fff3cd;
            color: #664d03;
        }

        /* Attachment styles are now in style.css */

        .attachments-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .attachment-item {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .attachment-preview {
            width: 120px;
            height: 120px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #dee2e6;
        }

        .attachment-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .pdf-preview {
            background-color: #f8f9fa;
            color: #dc3545;
            font-size: 2.5rem;
        }

        .attachment-info {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .attachment-meta {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .attachment-download {
            align-self: flex-start;
            padding: 5px 10px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .attachment-download:hover {
            background-color: #0b5ed7;
        }
    </style>

    <script>
        // Initialize filter values from URL parameters if they exist
        window.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('category')) document.getElementById('categoryFilter').value = urlParams.get('category');
            if (urlParams.has('unit')) document.getElementById('unitFilter').value = urlParams.get('unit');
            if (urlParams.has('status')) document.getElementById('statusFilter').value = urlParams.get('status');
            if (urlParams.has('date')) document.getElementById('dateFilter').value = urlParams.get('date');
            if (urlParams.has('search')) document.getElementById('searchInput').value = urlParams.get('search');

            // Load units from database
            loadUnitOptions();

            filterReports(); // Apply filters on page load

            // Add click event listeners to attachment indicators
            document.querySelectorAll('.attachment-indicator').forEach(indicator => {
                indicator.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent triggering the view details modal

                    const reportId = this.dataset.reportId;
                    let images = [];

                    try {
                        images = JSON.parse(this.dataset.images);
                    } catch (error) {
                        console.error('Error parsing image data:', error);
                        return;
                    }

                    if (images.length === 0) return;

                    // Populate attachments modal
                    const modal = document.getElementById('attachmentsModal');
                    const modalBody = modal.querySelector('.attachments-container');
                    modalBody.innerHTML = '';

                    images.forEach(image => {
                        const attachmentEl = document.createElement('div');
                        attachmentEl.className = 'attachment-item';

                        if (image.mimetype.startsWith('image/')) {
                            // Image attachment
                            attachmentEl.innerHTML = `
                                <div class="attachment-preview">
                                    <img src="${image.path}" alt="${image.originalName}">
                                </div>
                                <div class="attachment-info">
                                    <div class="attachment-name">${image.originalName}</div>
                                    <div class="attachment-meta">
                                        ${formatFileSize(image.size)} • ${image.mimetype.split('/')[1].toUpperCase()}
                                    </div>
                                    <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                        Download
                                    </a>
                                </div>
                            `;
                        } else if (image.mimetype === 'application/pdf') {
                            // PDF attachment
                            attachmentEl.innerHTML = `
                                <div class="attachment-preview pdf-preview">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="attachment-info">
                                    <div class="attachment-name">${image.originalName}</div>
                                    <div class="attachment-meta">
                                        ${formatFileSize(image.size)} • PDF
                                    </div>
                                    <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                        Download
                                    </a>
                                </div>
                            `;
                        }

                        modalBody.appendChild(attachmentEl);
                    });

                    // Show modal
                    modal.style.display = 'block';
                });
            });

            // Close attachment modal
            const attachmentsModal = document.getElementById('attachmentsModal');
            const attachmentsCloseBtn = attachmentsModal.querySelector('.close');

            attachmentsCloseBtn.addEventListener('click', () => {
                attachmentsModal.style.display = 'none';
            });

            window.addEventListener('click', e => {
                if (e.target === attachmentsModal) {
                    attachmentsModal.style.display = 'none';
                }
            });
        });

        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
            else return (bytes / 1048576).toFixed(1) + ' MB';
        }

// Add this function to fetch and populate unit dropdown
async function loadUnitOptions() {
    try {
        const response = await fetch('/intern/units');
        const data = await response.json();

        if (data.success) {
            const unitSelect = document.getElementById('unitFilter');
            // Store current selected value
            const currentValue = unitSelect.value;

            // Keep the "All Units" option
            const firstOption = unitSelect.querySelector('option[value=""]');
            unitSelect.innerHTML = '';
            unitSelect.appendChild(firstOption);

            // Add units from database
            data.units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.name;
                option.textContent = unit.name;
                // Restore selection if it matches
                if (unit.name === currentValue) {
                    option.selected = true;
                }
                unitSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading units:', error);
    }
}



        // Enhanced filter function
        function filterReports() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const category = document.getElementById('categoryFilter').value;
            const unit = document.getElementById('unitFilter').value;
            const status = document.getElementById('statusFilter').value;
            const date = document.getElementById('dateFilter').value;

            const rows = document.querySelectorAll('#reportsTable tbody tr');
            let visibleCount = 0;

            rows.forEach(row => {
                if (row.id === 'noResultsRow') return; // Skip the no results row

                // Use data attributes for consistent filtering on all screen sizes
                const rowData = {
                    date: row.getAttribute('data-date'),
                    title: row.querySelector('[data-label="Title"]').textContent.toLowerCase(),
                    category: row.getAttribute('data-category'),
                    unit: row.getAttribute('data-unit'),
                    details: row.querySelector('.preview-text').textContent.toLowerCase(),
                    status: row.getAttribute('data-status')
                };

                const matchesSearch = !searchTerm ||
                    rowData.title.includes(searchTerm) ||
                    rowData.details.includes(searchTerm);

                const matchesCategory = !category || rowData.category === category;
                const matchesUnit = !unit || rowData.unit === unit;
                const matchesStatus = !status || rowData.status === status;
                const matchesDate = !date || rowData.date === date;

                const isVisible = matchesSearch && matchesCategory &&
                                matchesUnit && matchesStatus && matchesDate;

                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });

            // Handle no results message
            const existingNoResults = document.getElementById('noResultsRow');
            if (existingNoResults) {
                existingNoResults.remove();
            }

            if (visibleCount === 0) {
                const tbody = document.querySelector('#reportsTable tbody');
                const tr = document.createElement('tr');
                tr.id = 'noResultsRow';
                tr.innerHTML = `
                    <td colspan="6" class="no-results" style="padding-left: 1rem !important;">
                        No reports found matching the selected filters
                    </td>
                `;
                tbody.appendChild(tr);
            }
        }

        // View issue details function
        function viewIssueDetails(button) {
            const row = button.closest('tr');
            const fullDetails = row.querySelector('.full-details').textContent;

            const modal = document.getElementById('issueDetailsModal');
            const modalBody = modal.querySelector('.modal-body');

            // Format the text with line breaks preserved
            const formattedText = fullDetails.replace(/\n/g, '<br>');
            modalBody.innerHTML = `<div class="issue-content">${formattedText}</div>`;
            modal.style.display = 'block';
        }

        // Update report status function
        async function updateReportStatus(selectElement, reportId) {
            const newStatus = selectElement.value;
            const row = selectElement.closest('tr');

            try {
                const response = await fetch(`/intern/update-report-status/${reportId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                if (!response.ok) throw new Error('Failed to update status');

                // Update row attributes and UI
                row.setAttribute('data-status', newStatus);

                // Disable select if status is Solved
                if (newStatus === 'Solved') {
                    selectElement.disabled = true;
                }

                // Update stats
                updateDashboardStats();

                showNotification('Status updated successfully', 'success');

            } catch (error) {
                console.error('Error:', error);
                showNotification('Failed to update status', 'error');
                // Revert select to previous value
                selectElement.value = row.getAttribute('data-status');
            }
        }

        // Update dashboard stats
        async function updateDashboardStats() {
            try {
                const response = await fetch('/intern/dashboard-stats');
                const stats = await response.json();

                // Update stats display
                document.querySelector('.stat-card:nth-child(1) p').textContent = stats.totalReports;
                document.querySelector('.stat-card:nth-child(2) p').textContent = stats.solvedReports;
                document.querySelector('.stat-card:nth-child(3) p').textContent = stats.pendingReports;
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Function to periodically check for updated units
        function setupUnitRefresh() {
            // Initial load
            loadUnitOptions();

            // Set up periodic refresh (every 30 seconds)
            setInterval(loadUnitOptions, 30000);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Load units from database for the dropdown and set up refresh
            setupUnitRefresh();

            // Filter event listeners
            ['categoryFilter', 'unitFilter', 'statusFilter', 'dateFilter'].forEach(id => {
                document.getElementById(id).addEventListener('change', filterReports);
            });

            document.getElementById('searchInput').addEventListener('input', debounce(filterReports, 300));

            // Modal close functionality
            const modal = document.getElementById('issueDetailsModal');
            const closeBtn = modal.querySelector('.close');

            closeBtn.onclick = () => modal.style.display = 'none';
            window.onclick = (e) => {
                if (e.target === modal) modal.style.display = 'none';
            };
        });

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc</span>
        </div>
    </footer>
</body>
</html>