const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 15
  },
  password: {
    type: String,
    required: true
  },
  plainPassword: {
    type: String,
    required: function() {
      return this.role === 'intern' || this.role === 'supervisor';
    }
  },
  role: {
    type: String,
    required: true,
    enum: ['admin', 'intern', 'supervisor']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// Match password method
userSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Add this to skip validation when just updating isActive
userSchema.pre('save', function(next) {
  if (this.isModified('isActive') && !this.isModified('password') && !this.isModified('plainPassword')) {
    // Skip validation when only updating isActive
    this.validateSync = function() { return null; };
  }
  next();
});

// Add index for case-insensitive username search
userSchema.index({ username: 1 }, { 
  unique: true, 
  collation: { locale: 'en', strength: 2 }
});

module.exports = mongoose.model('User', userSchema); 