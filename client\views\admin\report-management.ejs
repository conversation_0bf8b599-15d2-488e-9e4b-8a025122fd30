<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Management</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/responsive.css">
    <!-- Add Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXCH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/admin/dashboard">Dashboard</a>
            <a href="/admin/user-management">User Management</a>
            <a href="/admin/report-management" class="active">Report Management</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Report Management</h1>

        <!-- Add time period filter -->
        <div class="chart-filters responsive-filters">
            <div class="filter-group">
                <label for="timeFilter">Time Period</label>
                <select id="timeFilter" onchange="updateCharts()">
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="exportData()">Export Data</button>
        </div>

        <!-- Charts section -->
        <div class="charts-container responsive-charts">
            <div class="chart-row">
                <div class="chart-card">
                    <h3>Reports by Unit</h3>
                    <canvas id="unitChart"></canvas>
                </div>
                <div class="chart-card">
                    <h3>Issue Categories Distribution</h3>
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
            <div class="chart-row">
                <div class="chart-card">
                    <h3>Report Status Distribution</h3>
                    <canvas id="statusChart"></canvas>
                </div>
                <div class="chart-card">
                    <h3>Reports Trend Over Time</h3>
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>

        <div class="filters responsive-filters">
            <div class="filter-group">
                <label for="categoryFilter">Category</label>
                <select id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="Hardware">Hardware</option>
                    <option value="Network">Network</option>
                    <option value="Software">Software</option>
                    <option value="Other">Other</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="unitFilter">Unit</label>
                <select id="unitFilter">
                    <option value="">All Units</option>
                    <option value="Emergency">Emergency</option>
                    <option value="OPD">OPD</option>
                    <option value="IPD">IPD</option>
                    <option value="Laboratory">Laboratory</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Radiology">Radiology</option>
                    <option value="Administration">Administration</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="statusFilter">Status</label>
                <select id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="Solved">Solved</option>
                    <option value="Unsolved">Unsolved</option>
                    <option value="In Progress">In Progress</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="dateFilter">Date</label>
                <input type="date" id="dateFilter">
            </div>
        </div>

        <div class="actions-bar">
            <button id="deleteSelected" class="btn btn-danger" disabled>Delete Selected</button>
        </div>

        <div class="reports-table responsive-table report-management">
            <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th>Date</th>
                        <th>Intern</th>
                        <th>Unit</th>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Issue Details</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <% reports.forEach(report => { %>
                        <tr data-report-id="<%= report._id %>">
                            <td data-label="Select">
                                <input type="checkbox" class="report-checkbox">
                            </td>
                            <td data-label="Date"><%= new Date(report.timestamp).toLocaleDateString() %></td>
                            <td data-label="Intern"><%= report.username %></td>
                            <td data-label="Unit"><%= report.unit %></td>
                            <td data-label="Title"><%= report.title %></td>
                            <td data-label="Category"><%= report.category %></td>
                            <td data-label="Issue Details">
                                <div class="issue-details-preview">
                                    <div class="issue-text"><%= report.issueDetails.length > 100 ? report.issueDetails.substring(0, 100) + '...' : report.issueDetails %></div>
                                    <button class="btn-view-more">View More</button>
                                    <% if (report.images && report.images.length > 0) { %>
                                        <span class="attachment-indicator"
                                              data-report-id="<%= report._id %>"
                                              data-images="<%= JSON.stringify(report.images) %>">
                                            <i class="fas fa-paperclip"></i> <%= report.images.length %> attachment<%= report.images.length > 1 ? 's' : '' %>
                                        </span>
                                    <% } %>
                                </div>
                            </td>
                            <td data-label="Status" class="report-status"><%= report.status %></td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
            </div><!-- end table-container -->
        </div>
    </div>

    <!-- Modal for viewing full issue details -->
    <div id="issueDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Issue Details</h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Modal for viewing attachments -->
    <div id="attachmentsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Attachments</h2>
            <div class="modal-body attachments-container"></div>
        </div>
    </div>

    <script src="/js/main.js"></script>
    <script>
        // Chart initialization and data management
        let unitChart, categoryChart, statusChart, trendChart;
        let reportData = <%- JSON.stringify(reports) %>;

        function initializeCharts() {
            // Unit Distribution Chart
            const unitCtx = document.getElementById('unitChart').getContext('2d');
            unitChart = new Chart(unitCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Reports per Unit',
                        data: [],
                        backgroundColor: '#a31d5b',
                        borderColor: '#8a1749',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Category Distribution Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            categoryChart = new Chart(categoryCtx, {
                type: 'pie',
                data: {
                    labels: ['Hardware', 'Network', 'Software', 'Other'],
                    datasets: [{
                        data: [],
                        backgroundColor: ['#a31d5b', '#c12369', '#d4a1b3', '#e0a800']
                    }]
                },
                options: {
                    responsive: true
                }
            });

            // Status Distribution Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            statusChart = new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['Solved', 'Unsolved', 'In Progress'],
                    datasets: [{
                        data: [],
                        backgroundColor: ['#28a745', '#dc3545', '#ffc107']
                    }]
                },
                options: {
                    responsive: true
                }
            });

            // Trend Chart
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Number of Reports',
                        data: [],
                        borderColor: '#a31d5b',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updateCharts() {
            const timeFilter = document.getElementById('timeFilter').value;
            const filteredData = filterDataByTime(reportData, timeFilter);

            updateUnitChart(filteredData);
            updateCategoryChart(filteredData);
            updateStatusChart(filteredData);
            updateTrendChart(filteredData, timeFilter);
        }

        function filterDataByTime(data, period) {
            const now = new Date();
            const filtered = data.filter(report => {
                const reportDate = new Date(report.timestamp);
                switch(period) {
                    case 'daily':
                        return reportDate.toDateString() === now.toDateString();
                    case 'weekly':
                        const weekAgo = new Date(now - 7 * 24 * 60 * 60 * 1000);
                        return reportDate >= weekAgo;
                    case 'monthly':
                        return reportDate.getMonth() === now.getMonth() &&
                               reportDate.getFullYear() === now.getFullYear();
                }
            });
            return filtered;
        }

        function updateUnitChart(data) {
            const unitCounts = {};
            data.forEach(report => {
                unitCounts[report.unit] = (unitCounts[report.unit] || 0) + 1;
            });

            unitChart.data.labels = Object.keys(unitCounts);
            unitChart.data.datasets[0].data = Object.values(unitCounts);
            unitChart.update();
        }

        function updateCategoryChart(data) {
            const categoryCounts = {
                Hardware: 0,
                Network: 0,
                Software: 0,
                Other: 0
            };
            data.forEach(report => {
                categoryCounts[report.category]++;
            });

            categoryChart.data.datasets[0].data = Object.values(categoryCounts);
            categoryChart.update();
        }

        function updateStatusChart(data) {
            const statusCounts = {
                Solved: 0,
                Unsolved: 0,
                'In Progress': 0
            };
            data.forEach(report => {
                statusCounts[report.status]++;
            });

            statusChart.data.datasets[0].data = Object.values(statusCounts);
            statusChart.update();
        }

        function updateTrendChart(data, period) {
            const trends = {};
            data.forEach(report => {
                const date = new Date(report.timestamp);
                let key;
                switch(period) {
                    case 'daily':
                        key = date.toLocaleDateString();
                        break;
                    case 'weekly':
                        key = `Week ${Math.ceil(date.getDate() / 7)}`;
                        break;
                    case 'monthly':
                        key = date.toLocaleDateString('default', { month: 'short' });
                        break;
                }
                trends[key] = (trends[key] || 0) + 1;
            });

            trendChart.data.labels = Object.keys(trends);
            trendChart.data.datasets[0].data = Object.values(trends);
            trendChart.update();
        }

        function exportData() {
            const timeFilter = document.getElementById('timeFilter').value;
            const filteredData = filterDataByTime(reportData, timeFilter);

            // Create CSV content
            const headers = ['Date', 'Unit', 'Category', 'Status', 'Title', 'Details'];
            const csvContent = [
                headers.join(','),
                ...filteredData.map(report => [
                    new Date(report.timestamp).toLocaleDateString(),
                    report.unit,
                    report.category,
                    report.status,
                    `"${report.title.replace(/"/g, '""')}"`,
                    `"${report.issueDetails.replace(/"/g, '""').replace(/\n/g, ' ')}"`
                ].join(','))
            ].join('\n');

            // Create and trigger download
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `reports_${timeFilter}_${new Date().toLocaleDateString()}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeCharts();
            updateCharts();
        });

        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.report-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = this.checked);
            updateDeleteButton();
        });

        document.querySelectorAll('.report-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateDeleteButton);
        });

        function updateDeleteButton() {
            const selectedCount = document.querySelectorAll('.report-checkbox:checked').length;
            const deleteButton = document.getElementById('deleteSelected');
            deleteButton.disabled = selectedCount === 0;
            deleteButton.textContent = `Delete Selected (${selectedCount})`;
        }

        // Delete functionality
        document.getElementById('deleteSelected').addEventListener('click', async function() {
            if (!confirm('Are you sure you want to delete the selected reports?')) return;

            const selectedReports = Array.from(document.querySelectorAll('.report-checkbox:checked'))
                .map(checkbox => checkbox.closest('tr').dataset.reportId);

            try {
                const response = await fetch('/admin/delete-reports', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ reportIds: selectedReports })
                });

                if (response.ok) {
                    selectedReports.forEach(id => {
                        document.querySelector(`tr[data-report-id="${id}"]`).remove();
                    });
                    updateDeleteButton();
                    showNotification('Reports deleted successfully', 'success');
                } else {
                    throw new Error('Failed to delete reports');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Failed to delete reports', 'error');
            }
        });

        // Filter functionality
        function applyFilters() {
            const category = document.getElementById('categoryFilter').value;
            const unit = document.getElementById('unitFilter').value;
            const status = document.getElementById('statusFilter').value;
            const date = document.getElementById('dateFilter').value;

            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const rowDate = row.children[1].textContent;
                const rowIntern = row.children[2].textContent;
                const rowUnit = row.children[3].textContent;
                const rowCategory = row.children[5].textContent;
                const rowStatus = row.children[7].textContent;

                const matchesCategory = !category || rowCategory === category;
                const matchesUnit = !unit || rowUnit === unit;
                const matchesStatus = !status || rowStatus === status;
                const matchesDate = !date ||
                    new Date(rowDate).toLocaleDateString() === new Date(date).toLocaleDateString();

                row.style.display =
                    matchesCategory && matchesUnit && matchesStatus && matchesDate
                    ? ''
                    : 'none';
            });
        }

        // Add filter event listeners
        document.getElementById('categoryFilter').addEventListener('change', applyFilters);
        document.getElementById('unitFilter').addEventListener('change', applyFilters);
        document.getElementById('statusFilter').addEventListener('change', applyFilters);
        document.getElementById('dateFilter').addEventListener('change', applyFilters);

        // Modal functionality
        const modal = document.getElementById('issueDetailsModal');
        const modalBody = modal.querySelector('.modal-body');
        const closeBtn = modal.querySelector('.close');

        document.querySelectorAll('.btn-view-more').forEach(button => {
            button.addEventListener('click', function() {
                const reportId = this.closest('tr').dataset.reportId;
                const report = reportData.find(r => r._id === reportId);

                if (report) {
                    // Format the text with line breaks preserved
                    modalBody.innerHTML = '';
                    const textElement = document.createElement('div');
                    textElement.className = 'issue-details-text';

                    // Replace newlines with <br> tags for proper display
                    textElement.innerHTML = report.issueDetails.replace(/\n/g, '<br>');

                    modalBody.appendChild(textElement);
                    modal.style.display = 'block';
                }
            });
        });

        closeBtn.addEventListener('click', () => modal.style.display = 'none');
        window.addEventListener('click', e => {
            if (e.target === modal) modal.style.display = 'none';
        });

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        // Attachment handling
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to attachment indicators
            document.querySelectorAll('.attachment-indicator').forEach(indicator => {
                indicator.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent triggering the view details modal

                    const reportId = this.dataset.reportId;
                    let images = [];

                    try {
                        images = JSON.parse(this.dataset.images);
                    } catch (error) {
                        console.error('Error parsing image data:', error);
                        return;
                    }

                    if (images.length === 0) return;

                    // Populate attachments modal
                    const modal = document.getElementById('attachmentsModal');
                    const modalBody = modal.querySelector('.attachments-container');
                    modalBody.innerHTML = '';

                    images.forEach(image => {
                        const attachmentEl = document.createElement('div');
                        attachmentEl.className = 'attachment-item';

                        if (image.mimetype.startsWith('image/')) {
                            // Image attachment
                            attachmentEl.innerHTML = `
                                <div class="attachment-preview">
                                    <img src="${image.path}" alt="${image.originalName}">
                                </div>
                                <div class="attachment-info">
                                    <div class="attachment-name">${image.originalName}</div>
                                    <div class="attachment-meta">
                                        ${formatFileSize(image.size)} • ${image.mimetype.split('/')[1].toUpperCase()}
                                    </div>
                                    <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                        Download
                                    </a>
                                </div>
                            `;
                        } else if (image.mimetype === 'application/pdf') {
                            // PDF attachment
                            attachmentEl.innerHTML = `
                                <div class="attachment-preview pdf-preview">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="attachment-info">
                                    <div class="attachment-name">${image.originalName}</div>
                                    <div class="attachment-meta">
                                        ${formatFileSize(image.size)} • PDF
                                    </div>
                                    <a href="${image.path}" download="${image.originalName}" class="attachment-download">
                                        Download
                                    </a>
                                </div>
                            `;
                        }

                        modalBody.appendChild(attachmentEl);
                    });

                    // Show modal
                    modal.style.display = 'block';
                });
            });

            // Close attachment modal
            const attachmentsModal = document.getElementById('attachmentsModal');
            const attachmentsCloseBtn = attachmentsModal.querySelector('.close');

            attachmentsCloseBtn.addEventListener('click', () => {
                attachmentsModal.style.display = 'none';
            });

            window.addEventListener('click', e => {
                if (e.target === attachmentsModal) {
                    attachmentsModal.style.display = 'none';
                }
            });
        });

        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
            else return (bytes / 1048576).toFixed(1) + ' MB';
        }
    </script>

    <style>
        .charts-container {
            margin: 2rem 0;
        }

        .chart-row {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* Attachment styles are now in style.css */

        /* Attachment hover styles are now in style.css */

        .issue-text {
            white-space: pre-wrap;
            word-break: break-word;
            margin-bottom: 5px;
        }

        .issue-details-text {
            white-space: pre-wrap;
            word-break: break-word;
            line-height: 1.5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .attachment-indicator i {
            margin-right: 4px;
        }

        .attachments-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .attachment-item {
            display: flex;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .attachment-preview {
            width: 120px;
            height: 120px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #dee2e6;
        }

        .attachment-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .pdf-preview {
            background-color: #f8f9fa;
            color: #dc3545;
            font-size: 2.5rem;
        }

        .attachment-info {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .attachment-meta {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .attachment-download {
            align-self: flex-start;
            padding: 5px 10px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .attachment-download:hover {
            background-color: #0b5ed7;
        }

        .chart-card {
            flex: 1;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chart-card h3 {
            color: #a31d5b;
            margin-bottom: 1rem;
            text-align: center;
        }

        .chart-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        /* Responsive styles are in responsive.css */

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            color: white;
            z-index: 1000;
        }

        .notification.success {
            background-color: #28a745;
        }

        .notification.error {
            background-color: #dc3545;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 10px;
            font-size: 28px;
            cursor: pointer;
        }

        .btn-view-more {
            padding: 2px 8px;
            font-size: 0.8em;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
    </style>

    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc</span>
        </div>
    </footer>
</body>
</html>