<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - SFXCH IT Reporting System</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="icon" href="data:,">
    <style>
        body {
            background-color: #B6DEDF;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding-bottom: 4rem;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin-bottom: 4rem;
        }

        .login-box {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-logo {
            display: block;
            margin: 0 auto 1rem;
            max-width: 150px;
            height: auto;
        }

        .alert {
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-danger {
            background-color: #fde8e8;
            color: #dc3545;
            border: 1px solid #fad7d7;
        }

        .alert-success {
            background-color: #e8f5e9;
            color: #28a745;
            border: 1px solid #c8e6c9;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .btn-block {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <img src="/images/logo.jpeg" alt="Logo" class="login-logo">
            <h1>SFXH IT Reporting System</h1>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <%= error %>
                </div>
            <% } %>

            <% if (typeof success !== 'undefined' && success) { %>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <%= success %>
                </div>
            <% } %>

            <form action="/login" method="POST">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           required 
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           required 
                           autocomplete="current-password">
                </div>

                <button type="submit" class="btn btn-primary btn-block">Login</button>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc</span>
        </div>
    </footer>
</body>
</html> 