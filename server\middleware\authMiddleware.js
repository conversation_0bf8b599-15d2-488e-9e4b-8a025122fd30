const User = require('../models/User');

exports.checkUserStatus = async (req, res, next) => {
    try {
        // Skip check for admin and supervisor users
        if (req.session.user && (req.session.user.role === 'admin' || req.session.user.role === 'supervisor')) {
            return next();
        }

        // Check if user is logged in
        if (!req.session.user) {
            return res.redirect('/login');
        }

        // Get fresh user data from database
        const user = await User.findById(req.session.user.id);
        
        if (!user || !user.isActive) {
            // Clear session
            req.session.destroy();
            
            // Redirect to login with message
            return res.redirect('/login?error=Account disabled');
        }

        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.redirect('/login');
    }
}; 