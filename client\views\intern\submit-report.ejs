<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Report</title>
    <link rel="stylesheet" href="/css/style.css">
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <img src="/images/logo.jpeg" alt="Logo">
            SFXCH IT Reporting System
        </div>
        <div class="nav-links">
            <a href="/intern/dashboard">Dashboard</a>
            <a href="/intern/submit-report" class="active">Submit Report</a>
            <a href="/logout">Logout</a>
        </div>
    </nav>

    <div class="container">
        <h1>Submit New Report</h1>

        <form id="reportForm" class="report-form">
            <!-- Metadata section -->
            <div class="form-section metadata-section">
                <div class="form-row">
                    <div class="form-group short-select">
                        <label for="category">Category</label>
                        <select id="category" name="category" required>
                            <option value="">Select Category</option>
                            <option value="Hardware">Hardware</option>
                            <option value="Network">Network</option>
                            <option value="Software">Software</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="form-group short-select">
                        <label for="unit">Unit</label>
                        <select id="unit" name="unit" required>
                            <option value="">Select Unit</option>
                            <option value="Emergency">Emergency</option>
                            <option value="OPD">OPD</option>
                            <option value="IPD">IPD</option>
                            <option value="Laboratory">Laboratory</option>
                            <option value="Pharmacy">Pharmacy</option>
                            <option value="Radiology">Radiology</option>
                            <option value="Administration">Administration</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Content section -->
            <div class="form-section content-section">
                <div class="form-group">
                    <label for="title">Title</label>
                    <input type="text"
                           id="title"
                           name="title"
                           required
                           placeholder="Brief description of the issue"
                           maxlength="100">
                </div>

                <div class="form-group">
                    <label for="issueDetails">Issue Details</label>
                    <div class="editor-container">
                        <textarea id="issueDetails" name="issueDetails" required rows="10" placeholder="Describe the issue in detail..."></textarea>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label for="images" class="form-label">Upload Images (Max 3)</label>
                    <div class="image-upload-container">
                        <div class="image-preview-area" id="imagePreviewArea"></div>
                        <div class="upload-controls">
                            <input type="file" class="form-control" id="images" name="images"
                                   accept=".jpg,.jpeg,.png,.pdf" multiple
                                   style="display: none;">
                            <button type="button" class="btn btn-outline-primary" id="uploadButton">
                                <i class="fas fa-cloud-upload-alt"></i> Choose Files
                            </button>
                            <small class="text-muted d-block mt-2">
                                Supported formats: JPEG, PNG, PDF (Max 5MB each)
                            </small>
                            <div id="uploadError" class="text-danger mt-2" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" class="btn btn-primary" id="submitBtn">Submit Report</button>
        </form>
    </div>

    <script>
        // Function to escape HTML tags
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.textContent;
        }

        // File upload handling
        let selectedFiles = [];

        // Handle file selection
        document.getElementById('uploadButton').addEventListener('click', function() {
            document.getElementById('images').click();
        });

        // Handle file input change
        document.getElementById('images').addEventListener('change', function(e) {
            const files = e.target.files;
            const maxFiles = 3;
            const uploadError = document.getElementById('uploadError');
            const previewArea = document.getElementById('imagePreviewArea');

            // Validate file count
            if (selectedFiles.length + files.length > maxFiles) {
                uploadError.textContent = `You can only upload a maximum of ${maxFiles} files.`;
                uploadError.style.display = 'block';
                return;
            }

            uploadError.style.display = 'none';

            // Process each file
            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Validate file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    uploadError.textContent = `File ${file.name} exceeds the 5MB size limit.`;
                    uploadError.style.display = 'block';
                    continue;
                }

                // Add to selected files
                selectedFiles.push(file);

                // Create preview element
                const preview = document.createElement('div');
                preview.className = 'image-preview';

                if (file.type.startsWith('image/')) {
                    // Image preview
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    preview.appendChild(img);
                } else if (file.type === 'application/pdf') {
                    // PDF preview
                    preview.classList.add('pdf-preview');
                    const icon = document.createElement('div');
                    icon.innerHTML = '<i class="fas fa-file-pdf"></i>';
                    const fileName = document.createElement('div');
                    fileName.textContent = file.name;
                    fileName.style.marginTop = '10px';
                    fileName.style.fontSize = '12px';
                    preview.appendChild(icon);
                    preview.appendChild(fileName);
                }

                // Add remove button
                const removeBtn = document.createElement('div');
                removeBtn.className = 'remove-image';
                removeBtn.innerHTML = '&times;';
                removeBtn.dataset.index = selectedFiles.length - 1;
                removeBtn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    selectedFiles.splice(index, 1);
                    preview.remove();
                    // Update indices for remaining remove buttons
                    document.querySelectorAll('.remove-image').forEach((btn, idx) => {
                        btn.dataset.index = idx;
                    });
                });

                preview.appendChild(removeBtn);
                previewArea.appendChild(preview);
            }

            // Reset file input
            e.target.value = '';
        });

        // Form submission handler
        document.getElementById('reportForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const issueDetailsElement = document.getElementById('issueDetails');
            const content = issueDetailsElement.value;

            if (!content.trim()) {
                showNotification('Please enter issue details', 'error');
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';

            try {
                // Create FormData object for file uploads
                const formData = new FormData();
                formData.append('title', document.getElementById('title').value);
                formData.append('category', document.getElementById('category').value);
                formData.append('unit', document.getElementById('unit').value);

                // Use plain text for issue details
                formData.append('issueDetails', content);

                // Add selected files
                selectedFiles.forEach(file => {
                    formData.append('images', file);
                });

                const response = await fetch('/intern/submit-report', {
                    method: 'POST',
                    body: formData // No Content-Type header needed, browser sets it with boundary
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Report submitted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/intern/dashboard';
                    }, 1500);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification(error.message || 'Error submitting report', 'error');
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Report';
            }
        });

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }





        // Add this function to fetch and populate unit dropdown
async function loadUnitOptions() {
    try {
        const response = await fetch('/intern/units');
        const data = await response.json();

        if (data.success) {
            const unitSelect = document.getElementById('unit');
            // Keep the "Select Unit" option
            const firstOption = unitSelect.querySelector('option[value=""]');
            unitSelect.innerHTML = '';
            unitSelect.appendChild(firstOption);

            // Add units from database
            data.units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.name;
                option.textContent = unit.name;
                unitSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading units:', error);
    }
}

// Call this function when page loads
document.addEventListener('DOMContentLoaded', loadUnitOptions);
    </script>

    <style>
        .report-form {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .metadata-section {
            background: #fff;
            border: 1px solid #e9ecef;
        }

        .form-row {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .short-select {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.15s ease-in-out;
        }

        .form-group input:focus,
        .form-group select:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .editor-container {
            border: 1px solid #ced4da;
            border-radius: 4px;
            overflow: hidden;
        }

        #issueDetails {
            width: 100%;
            padding: 12px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            border: none;
            resize: vertical;
            min-height: 200px;
        }

        .btn-primary {
            display: block;
            width: 100%;
            padding: 1rem;
            background: #0d6efd;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
        }

        .btn-primary:hover {
            background: #0b5ed7;
        }

        .btn-primary:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background: #198754;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.fade-out {
            animation: slideOut 0.3s ease forwards;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 1rem;
            }

            .short-select {
                min-width: 100%;
            }

            .report-form {
                margin: 1rem;
            }
        }

        .image-upload-container {
            border: 2px dashed #ccc;
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
            margin-bottom: 1rem;
        }

        .image-preview-area {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .image-preview {
            position: relative;
            width: 150px;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-preview .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            padding: 5px;
            cursor: pointer;
            color: #dc3545;
        }

        .upload-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #e9ecef;
        }

        .upload-progress-bar {
            height: 100%;
            background: #0d6efd;
            width: 0;
            transition: width 0.3s ease;
        }

        .pdf-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .pdf-preview i {
            font-size: 3rem;
            color: #dc3545;
        }
    </style>

    <footer class="footer">
        <div class="footer-signature">
            Design by <span>Pulse Tech Inc  @911</span>
        </div>
    </footer>
</body>
</html>