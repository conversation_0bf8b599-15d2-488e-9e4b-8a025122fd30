/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #b6dedf;  /* Updated background color */
}

/* Navigation */
.navbar {
    background-color: #a31d5b;  /* Updated navbar color */
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-brand img {
    height: 40px;
    width: auto;
}

.nav-links a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    margin-left: 1rem;
    transition: background-color 0.3s ease;
    border-radius: 4px;
}

.nav-links a:hover {
    background-color: #c12369;  /* Lighter shade of title bar color for hover */
}

.nav-links a.active {
    background-color: #8a1749;  /* Darker shade of title bar color for active */
    border-radius: 4px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    padding-bottom: 4rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #a31d5b;
    outline: none;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #a31d5b;
    color: white;
}

.btn-primary:hover {
    background-color: #c12369;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Login form */
.login-form {
    max-width: 400px;
    margin: 4rem auto;
    padding: 2rem;
    background-color: #b6dedf;;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.login-form h1 {
    color: #a31d5b;
    margin-bottom: 2rem;
    text-align: center;
}

.login-form img {
    display: block;
    margin: 0 auto 2rem;
    height: 80px;
    width: auto;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background-color: white;
}

th, td {
    padding: 0.75rem;
    border: 1px solid #ddd;
    text-align: left;
}

th {
    background-color: #a31d5b;
    color:white;
}

tr:hover {
    background-color: #f8f9fa;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filters select,
.filters input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Dark mode styles */
.dark-mode {
    background-color: #1a1a1a;
    color: #ffffff;
}

.dark-mode .navbar {
    background-color: #8a1749;
}

.dark-mode .container {
    background-color: #2d2d2d;
}

.dark-mode table {
    background-color: #2d2d2d;
    color: #ffffff;
}

.dark-mode th {
    background-color: #1a1a1a;
}

.dark-mode td {
    border-color: #404040;
}

.dark-mode .login-form,
.dark-mode .report-form {
    background-color: #2d2d2d;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
}

.dark-mode input,
.dark-mode select,
.dark-mode textarea {
    background-color: #1a1a1a;
    color: #ffffff;
    border-color: #404040;
}

.dark-mode .btn-primary {
    background-color: #a31d5b;
}

.dark-mode .btn-primary:hover {
    background-color: #c12369;
}

.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #333;
    color: #fff;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Responsive styles */
@media (max-width: 768px) {
    .filters {
        flex-direction: column;
    }

    table {
        display: block;
    }

    thead {
        display: none;
    }

    tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #ddd;
    }

    td {
        display: block;
        text-align: right;
        padding-left: 50%;
        position: relative;
    }

    td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        padding-left: 15px;
        font-weight: bold;
        text-align: left;
    }
}

/* Add these to your existing styles */
.btn-warning {
    background-color: #ffc107;
    color: #000;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.status-select {
    padding: 0.3rem;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.reports-management {
    margin-top: 3rem;
}

/* Update button styles */
.btn-enable {
    background-color: #c28c9e;
    color: white;
}

.btn-enable:hover {
    background-color: #d4a1b3;
}

.btn-disable {
    background-color: #ba768c;
    color: white;
}

.btn-disable:hover {
    background-color: #ce8aa0;
}

/* Form layout improvements */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.short-input {
    width: 200px;
}

.short-select {
    width: 150px;
}

/* Fix for enable/disable buttons */
.btn-enable, .btn-disable {
    min-width: 80px;
    text-align: center;
}

/* Table improvements */
.reports-table {
    overflow-x: auto;
}

.reports-table table {
    min-width: 1000px;
}

.reports-table td {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Rich text editor styles */
.tox-tinymce {
    border-radius: 4px !important;
}

.my-reports {
    margin-top: 3rem;
}

.my-reports h2 {
    color: #a31d5b;
    margin-bottom: 1rem;
}

.status-select {
    width: 120px;
    padding: 0.3rem;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.status-select:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

/* Report title input */
input[name="title"] {
    font-size: 1.1em;
    padding: 0.6rem;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 28px;
    cursor: pointer;
}

/* Issue details preview */
.issue-details-preview {
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.btn-view-more {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
    padding: 2px 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

/* Actions bar */
.actions-bar {
    margin: 1rem 0;
    display: flex;
    gap: 1rem;
}

/* Editor container */
.editor-container {
    position: relative;
    min-height: 300px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.resize-handle {
    height: 10px;
    background: #f5f5f5;
    border-top: 1px solid #ddd;
    cursor: row-resize;
    position: absolute;
    bottom: 0;
    width: 100%;
}

/* Hidden textarea for validation */
.hidden-textarea {
    display: none;
    visibility: hidden;
    height: 0;
    width: 0;
    padding: 0;
    margin: 0;
    border: none;
}

/* TinyMCE editor improvements */
.tox-tinymce {
    min-height: 300px !important;
}

.tox .tox-edit-area__iframe {
    background-color: white !important;
}

/* Resize handle improvements */
.editor-container {
    position: relative;
    margin-bottom: 1rem;
}

.resize-handle {
    height: 6px;
    background: #f0f0f0;
    border-top: 1px solid #ddd;
    cursor: row-resize;
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    z-index: 1;
}

.resize-handle:hover {
    background: #e0e0e0;
}

/* Responsive design */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }
}

@media (max-width: 992px) {
    .form-row {
        flex-wrap: wrap;
    }

    .short-select, .short-input {
        width: calc(50% - 0.5rem);
    }

    .filters {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .filter-group {
        flex: 1 1 calc(50% - 0.5rem);
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        text-align: center;
        padding: 1rem 0;
    }

    .nav-brand {
        margin-bottom: 1rem;
    }

    .nav-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .nav-links a {
        margin: 0;
    }

    .short-select, .short-input {
        width: 100%;
    }

    .filter-group {
        flex: 1 1 100%;
    }

    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .reports-table {
        margin: 1rem -1rem;
        padding: 0 1rem;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}

/* Filter group styles */
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    color: #666;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    color: #666;
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 2rem;
    font-weight: bold;
    color: #a31d5b;
    margin: 0;
}

.recent-reports {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.status-solved {
    color: #28a745;
}

.status-pending {
    color: #ffc107;
}

.status-progress {
    color: #17a2b8;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

th {
    background-color: #2c3e50;
    font-weight: 600;
    
}

/* Add this to your existing style.css */
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 0.75rem 0;
    font-size: 0.9rem;
    z-index: 1000;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
}

.footer-signature {
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
}

.footer-signature span {
    color: #B6DEDF;
    font-weight: bold;
}

.error {
    border-color: #dc3545 !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease;
    max-width: 350px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.notification.success {
    background-color: #198754;
}

.notification.error {
    background-color: #dc3545;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Improved attachment indicator styles */
.attachment-indicator {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    padding: 4px 10px;
    background-color: #e9ecef;
    border-radius: 12px;
    font-size: 0.75rem;
    color: #495057;
    cursor: pointer;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.attachment-indicator:hover {
    background-color: #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.attachment-indicator i {
    margin-right: 4px;
    color: #a31d5b;
}

/* Improved issue details preview */
.issue-details-preview {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    position: relative;
    padding-right: 0.5rem;
}

.preview-text, .issue-text {
    flex: 1;
    min-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Supervisor-specific styles */
.supervisor-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
    text-transform: capitalize;
}

.supervisor-status.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.supervisor-status.approved {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.supervisor-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.supervisor-status.na {
    background-color: #e2e3e5;
    color: #6c757d;
    border: 1px solid #d6d8db;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.approved {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.btn-approve {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85em;
    margin-right: 5px;
}

.btn-approve:hover {
    background-color: #218838;
}

.btn-reject {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85em;
    margin-right: 5px;
}

.btn-reject:hover {
    background-color: #c82333;
}

.btn-view-comments {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75em;
    margin-left: 5px;
}

.btn-view-comments:hover {
    background-color: #138496;
}

.approved-row {
    background-color: #f8fff9;
}

.rejected-row {
    background-color: #fff8f8;
}

.report-detail-item {
    margin-bottom: 15px;
    padding: 10px;
    border-left: 3px solid #007bff;
    background-color: #f8f9fa;
}

.report-detail-item strong {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

.attachments-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.attachment-item {
    display: block;
    text-decoration: none;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
    transition: box-shadow 0.2s;
}

.attachment-item:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attachment-item img {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 2px;
}

.attachments-section {
    margin-top: 20px;
}

.attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.attachments-grid .attachment-item {
    text-align: center;
    padding: 10px;
}

.attachments-grid .attachment-item span {
    display: block;
    margin-top: 5px;
    font-size: 0.8em;
    color: #6c757d;
    word-break: break-word;
}

.issue-details-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
    margin: 10px 0;
    line-height: 1.6;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background-color: #5a6268;
}

.no-reports {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}